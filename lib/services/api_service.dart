import 'dart:convert';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'config_service.dart';

/// Base API service for communicating with AWS backend
class ApiService {
  static ApiService? _instance;
  static ApiService get instance => _instance ??= ApiService._();

  ApiService._();

  /// Get the current server URL from ConfigService
  Future<String> getServerUrl() async {
    return await ConfigService.instance.getServerUrl();
  }



  /// Make an HTTP request to the SAM backend
  Future<http.Response> makeRequest({
    required String method,
    required String path,
    Map<String, String>? headers,
    Map<String, dynamic>? body,
    String? accessToken,
  }) async {
    try {
      final serverUrl = await getServerUrl();

      // Ensure path starts with /
      final cleanPath = path.startsWith('/') ? path : '/$path';
      final url = '$serverUrl$cleanPath';
      final uri = Uri.parse(url);

      final requestHeaders = <String, String>{
        'Content-Type': 'application/json',
        ...?headers,
      };

      if (accessToken != null) {
        requestHeaders['Authorization'] = 'Bearer $accessToken';
      }

      developer.log('API Request: $method $url');
      print('🔍 DEBUG: API Request URL: $url');
      print('🔍 DEBUG: Request Headers: $requestHeaders');
      if (body != null) {
        print('🔍 DEBUG: Request Body: ${json.encode(body)}');
      }

      http.Response response;

      switch (method.toUpperCase()) {
        case 'GET':
          response = await http.get(uri, headers: requestHeaders);
          break;
        case 'POST':
          response = await http.post(
            uri,
            headers: requestHeaders,
            body: body != null ? json.encode(body) : null,
          );
          break;
        case 'PUT':
          response = await http.put(
            uri,
            headers: requestHeaders,
            body: body != null ? json.encode(body) : null,
          );
          break;
        case 'DELETE':
          response = await http.delete(uri, headers: requestHeaders);
          break;
        default:
          throw Exception('Unsupported HTTP method: $method');
      }

      developer.log('API Response: ${response.statusCode}');
      print('🔍 DEBUG: Response Status: ${response.statusCode}');
      print('🔍 DEBUG: Response Body: ${response.body}');

      return response;
    } catch (e) {
      developer.log('API Request failed: $e');
      rethrow;
    }
  }

  /// Parse JSON response and handle errors
  Map<String, dynamic> parseResponse(http.Response response) {
    if (response.statusCode >= 200 && response.statusCode < 300) {
      // Handle empty response body
      if (response.body.isEmpty) {
        throw ApiException(
          message: 'Empty response from server',
          statusCode: response.statusCode,
          details: {},
        );
      }
      return json.decode(response.body) as Map<String, dynamic>;
    } else {
      final errorBody =
          response.body.isNotEmpty
              ? json.decode(response.body) as Map<String, dynamic>
              : <String, dynamic>{};

      final errorMessage =
          errorBody['error'] as String? ??
          errorBody['message'] as String? ??
          'HTTP ${response.statusCode}';

      throw ApiException(
        message: errorMessage,
        statusCode: response.statusCode,
        details: errorBody,
      );
    }
  }

  /// Clear cached values (useful for testing or environment changes)
  void clearCache() {
    ConfigService.instance.clearCache();
  }
}

/// Custom exception for API errors
class ApiException implements Exception {
  final String message;
  final int statusCode;
  final Map<String, dynamic> details;

  ApiException({
    required this.message,
    required this.statusCode,
    this.details = const {},
  });

  @override
  String toString() => 'ApiException: $message (HTTP $statusCode)';
}
