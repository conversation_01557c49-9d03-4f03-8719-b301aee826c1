import 'dart:developer' as developer;
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Configuration service for managing app settings and server URLs
class ConfigService {
  static ConfigService? _instance;
  static ConfigService get instance => _instance ??= ConfigService._();

  ConfigService._();

  // Environment configuration
  static const bool _isStaging = bool.fromEnvironment(
    'STAGING',
    defaultValue: false,
  );

  static const bool _isDevelopment = bool.fromEnvironment(
    'DEVELOPMENT',
    defaultValue: true,
  );

  // Configuration keys
  static const String _serverUrlKey = 'gameflex_server_url';
  static const String _autoDetectKey = 'gameflex_auto_detect_url';
  static const String _selectedUrlOptionKey = 'gameflex_selected_url_option';

  // API Gateway/Lambda URL options
  static const String _localhostApiUrl = 'http://127.0.0.1:4566/restapis/exrwgvj7st/prod/_user_request_';  // LocalStack API Gateway
  static const String _androidEmulatorApiUrl = 'http://********:4566/restapis/exrwgvj7st/prod/_user_request_';  // LocalStack for Android emulator
  static const String _stagingApiUrl = 'http://dev.api.gameflex.io:8000';
  static const String _productionApiUrl = 'https://api.gameflex.io';

  // S3/Media URL options
  static const String _localhostS3Url = 'http://127.0.0.1:8787';  // Cloudflare R2 Dev
  static const String _androidEmulatorS3Url = 'http://********:8787';  // Cloudflare R2 Dev for Android emulator
  static const String _stagingS3Url = 'http://media.dev.gameflex.io';  // Future staging subdomain
  static const String _productionS3Url = 'https://media.gameflex.io';  // Future production subdomain

  String? _cachedServerUrl;
  String? _cachedS3Url;

  /// Check if app is in development mode
  bool get isDevelopment => _isDevelopment && !_isStaging;

  /// Check if app is in staging mode
  bool get isStaging => _isStaging;

  /// Check if app is in production mode
  bool get isProduction => !_isDevelopment && !_isStaging;

  /// Get the current API server URL based on environment and user preferences
  Future<String> getServerUrl() async {
    if (_cachedServerUrl != null) {
      return _cachedServerUrl!;
    }

    // In production mode, always use production URL
    if (isProduction) {
      _cachedServerUrl = _productionApiUrl;
      return _cachedServerUrl!;
    }

    // In staging mode, always use staging URL
    if (isStaging) {
      _cachedServerUrl = _stagingApiUrl;
      return _cachedServerUrl!;
    }

    // In development mode, check user preferences
    final prefs = await SharedPreferences.getInstance();
    final autoDetect = prefs.getBool(_autoDetectKey) ?? true;

    if (autoDetect) {
      _cachedServerUrl = await _getAutoDetectedApiUrl();
    } else {
      _cachedServerUrl =
          prefs.getString(_serverUrlKey) ?? await _getAutoDetectedApiUrl();
    }

    developer.log('ConfigService: Using server URL: $_cachedServerUrl');
    return _cachedServerUrl!;
  }

  /// Get the current S3/Media URL based on environment and user preferences
  /// This follows the same selection logic as the API URL
  Future<String> getS3Url() async {
    if (_cachedS3Url != null) {
      return _cachedS3Url!;
    }

    // In production mode, always use production S3 URL
    if (isProduction) {
      _cachedS3Url = _productionS3Url;
      return _cachedS3Url!;
    }

    // In staging mode, always use staging S3 URL
    if (isStaging) {
      _cachedS3Url = _stagingS3Url;
      return _cachedS3Url!;
    }

    // In development mode, follow the same logic as API URL selection
    final prefs = await SharedPreferences.getInstance();
    final autoDetect = prefs.getBool(_autoDetectKey) ?? true;

    if (autoDetect) {
      _cachedS3Url = await _getAutoDetectedS3Url();
    } else {
      // Get the manually selected server URL and map it to corresponding S3 URL
      final selectedServerUrl = prefs.getString(_serverUrlKey);
      _cachedS3Url = _mapApiUrlToS3Url(selectedServerUrl) ?? await _getAutoDetectedS3Url();
    }

    return _cachedS3Url!;
  }

  /// Auto-detect the appropriate API server URL based on platform
  Future<String> _getAutoDetectedApiUrl() async {
    if (kIsWeb) {
      return _localhostApiUrl;
    } else if (defaultTargetPlatform == TargetPlatform.android) {
      // Check if running on emulator
      if (await isRunningOnEmulator()) {
        return _androidEmulatorApiUrl;
      } else {
        // Running on physical device, use localhost
        return _localhostApiUrl;
      }
    } else {
      // Windows, iOS, macOS, Linux - use localhost
      return _localhostApiUrl;
    }
  }

  /// Auto-detect the appropriate S3 URL based on platform
  Future<String> _getAutoDetectedS3Url() async {
    if (kIsWeb) {
      return _localhostS3Url;
    } else if (defaultTargetPlatform == TargetPlatform.android) {
      // Check if running on emulator
      if (await isRunningOnEmulator()) {
        return _androidEmulatorS3Url;
      } else {
        // Running on physical device, use localhost
        return _localhostS3Url;
      }
    } else {
      // Windows, iOS, macOS, Linux - use localhost
      return _localhostS3Url;
    }
  }

  /// Map API URL to corresponding S3 URL
  String? _mapApiUrlToS3Url(String? apiUrl) {
    if (apiUrl == null) return null;

    switch (apiUrl) {
      case _localhostApiUrl:
        return _localhostS3Url;
      case _androidEmulatorApiUrl:
        return _androidEmulatorS3Url;
      case _stagingApiUrl:
        return _stagingS3Url;
      case _productionApiUrl:
        return _productionS3Url;
      default:
        return null;
    }
  }

  /// Check if running on Android emulator
  Future<bool> isRunningOnEmulator() async {
    if (!Platform.isAndroid) return false;

    try {
      // Method 1: Check for emulator characteristics using getprop
      final result = await Process.run('getprop', ['ro.kernel.qemu']);
      if (result.stdout.toString().trim() == '1') {
        return true;
      }

      // Method 2: Check for emulator-specific properties
      final buildResult = await Process.run('getprop', [
        'ro.build.fingerprint',
      ]);
      final fingerprint = buildResult.stdout.toString().toLowerCase();
      if (fingerprint.contains('generic') || fingerprint.contains('emulator')) {
        return true;
      }

      // Method 3: Check hardware name
      final hardwareResult = await Process.run('getprop', ['ro.hardware']);
      final hardware = hardwareResult.stdout.toString().toLowerCase();
      if (hardware.contains('goldfish') || hardware.contains('ranchu')) {
        return true;
      }

      return false;
    } catch (e) {
      // Fallback: assume emulator if we can't determine
      developer.log(
        'ConfigService: Could not determine if running on emulator: $e',
      );
      return true;
    }
  }

  /// Set server URL manually (for development dropdown)
  Future<void> setServerUrl(String url, String optionName) async {
    if (!isDevelopment) {
      developer.log(
        'ConfigService: Cannot change server URL in non-development mode',
      );
      return;
    }

    final prefs = await SharedPreferences.getInstance();

    if (url == 'auto') {
      await prefs.setBool(_autoDetectKey, true);
      await prefs.remove(_serverUrlKey);
      await prefs.setString(_selectedUrlOptionKey, optionName);
      _cachedServerUrl = null; // Reset cache to trigger auto-detection
      _cachedS3Url = null; // Also reset S3 cache
    } else {
      await prefs.setString(_serverUrlKey, url);
      await prefs.setBool(_autoDetectKey, false);
      await prefs.setString(_selectedUrlOptionKey, optionName);
      _cachedServerUrl = url;
      _cachedS3Url = null; // Reset S3 cache to use new mapping
    }

    developer.log('ConfigService: Server URL set to: $url ($optionName)');
  }

  /// Get the currently selected URL option name
  Future<String> getSelectedUrlOption() async {
    if (!isDevelopment) {
      return isStaging ? 'Staging' : 'Production';
    }

    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_selectedUrlOptionKey) ?? 'Auto-detect';
  }

  /// Enable auto-detection of server URL
  Future<void> enableAutoDetection() async {
    if (!isDevelopment) return;

    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_autoDetectKey, true);
    await prefs.remove(_serverUrlKey);
    await prefs.setString(_selectedUrlOptionKey, 'Auto-detect');
    _cachedServerUrl = null; // Reset cache to trigger auto-detection

    developer.log('ConfigService: Auto-detection enabled');
  }

  /// Check if auto-detection is enabled
  Future<bool> isAutoDetectionEnabled() async {
    if (!isDevelopment) return false;

    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_autoDetectKey) ?? true;
  }

  /// Get available server URL options for development dropdown
  List<ServerUrlOption> getServerUrlOptions() {
    if (!isDevelopment) {
      // In production/staging, only show current environment
      return [
        ServerUrlOption(
          name: isStaging ? 'Staging' : 'Production',
          url: isStaging ? _stagingApiUrl : _productionApiUrl,
          description:
              isStaging
                  ? 'Development staging environment'
                  : 'Production environment',
        ),
      ];
    }

    return [
      ServerUrlOption(
        name: 'Auto-detect',
        url: 'auto',
        description: 'Automatically detect based on platform',
      ),
      ServerUrlOption(
        name: 'LocalStack (Localhost)',
        url: _localhostApiUrl,
        description: 'LocalStack API Gateway on localhost',
      ),
      ServerUrlOption(
        name: 'LocalStack (Android Emulator)',
        url: _androidEmulatorApiUrl,
        description: 'LocalStack API Gateway for Android emulator',
      ),
      ServerUrlOption(
        name: 'Staging',
        url: _stagingApiUrl,
        description: 'Development staging server',
      ),
      ServerUrlOption(
        name: 'Production',
        url: _productionApiUrl,
        description: 'Production server (HTTPS)',
      ),
    ];
  }

  /// Get environment display name
  String getEnvironmentName() {
    if (isProduction) return 'Production';
    if (isStaging) return 'Staging';
    return 'Development';
  }

  /// Get environment color for UI
  String getEnvironmentColor() {
    if (isProduction) return '#FF5722'; // Red
    if (isStaging) return '#FF9800'; // Orange
    return '#4CAF50'; // Green
  }

  /// Clear cached values (useful for testing or environment changes)
  void clearCache() {
    _cachedServerUrl = null;
    _cachedS3Url = null;
  }

  /// Reset all configuration to defaults
  Future<void> resetToDefaults() async {
    if (!isDevelopment) return;

    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_serverUrlKey);
    await prefs.remove(_autoDetectKey);
    await prefs.remove(_selectedUrlOptionKey);

    clearCache();
    developer.log('ConfigService: Configuration reset to defaults');
  }

  /// Get debug information
  Future<Map<String, dynamic>> getDebugInfo() async {
    final prefs = await SharedPreferences.getInstance();

    return {
      'environment': getEnvironmentName(),
      'isDevelopment': isDevelopment,
      'isStaging': isStaging,
      'isProduction': isProduction,
      'currentServerUrl': await getServerUrl(),
      'currentS3Url': await getS3Url(),
      'selectedOption': await getSelectedUrlOption(),
      'autoDetectEnabled': await isAutoDetectionEnabled(),
      'isEmulator': await isRunningOnEmulator(),
      'platform': defaultTargetPlatform.toString(),
      'isWeb': kIsWeb,
      'storedServerUrl': prefs.getString(_serverUrlKey),
      'storedAutoDetect': prefs.getBool(_autoDetectKey),
      'storedSelectedOption': prefs.getString(_selectedUrlOptionKey),
    };
  }


}

/// Server URL option model
class ServerUrlOption {
  final String name;
  final String url;
  final String description;

  ServerUrlOption({
    required this.name,
    required this.url,
    required this.description,
  });

  @override
  String toString() => name;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ServerUrlOption &&
          runtimeType == other.runtimeType &&
          name == other.name &&
          url == other.url;

  @override
  int get hashCode => name.hashCode ^ url.hashCode;
}
