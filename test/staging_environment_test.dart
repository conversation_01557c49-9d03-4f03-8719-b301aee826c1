import 'package:flutter_test/flutter_test.dart';
import 'package:gameflex_mobile/services/config_service.dart';

// Note: This test file is designed to be run with --dart-define=STAGING=true
// to test staging environment configuration

void main() {
  group('Staging Environment Configuration Tests (AWS Backend)', () {
    late ConfigService configService;

    setUpAll(() async {
      TestWidgetsFlutterBinding.ensureInitialized();
      configService = ConfigService.instance;
    });

    test('Staging environment should use staging URLs', () async {
      // This test should be run with: flutter test --dart-define=STAGING=true
      const isStaging = bool.fromEnvironment('STAGING', defaultValue: false);

      if (!isStaging) {
        // Skip this test if not running in staging mode
        return;
      }

      final serverUrl = await configService.getServerUrl();
      final environment = configService.getEnvironmentName();

      // Should use staging URL
      expect(
        serverUrl.contains('dev.api.gameflex.io') || environment == 'Staging',
        true,
        reason: 'Staging should use staging URLs, got: $serverUrl',
      );

      // Should not use local URLs
      expect(
        serverUrl.contains('localhost') || serverUrl.contains('********'),
        false,
        reason: 'Staging should not use local URLs, got: $serverUrl',
      );
    });

    test('Staging configuration should be correct', () {
      const isStaging = bool.fromEnvironment('STAGING', defaultValue: false);

      if (!isStaging) {
        // Skip this test if not running in staging mode
        return;
      }

      final environment = configService.getEnvironmentName();
      final isConfigStaging = configService.isStaging;
      final isDevelopment = configService.isDevelopment;
      final isProduction = configService.isProduction;

      // Should be in staging mode
      expect(isConfigStaging, true, reason: 'Should be in staging mode');
      expect(isDevelopment, false, reason: 'Should not be in development mode');
      expect(isProduction, false, reason: 'Should not be in production mode');
      expect(
        environment,
        equals('Staging'),
        reason: 'Environment should be Staging',
      );
    });
  });
}
