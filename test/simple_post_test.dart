import 'package:flutter_test/flutter_test.dart';
import 'package:gameflex_mobile/services/aws_auth_service.dart';
import 'package:gameflex_mobile/services/aws_posts_service.dart';
import 'test_helpers.dart';

void main() {
  group('Simple Post Creation Tests (AWS Backend)', () {
    late AwsAuthService authService;
    late AwsPostsService postsService;

    setUpAll(() async {
      // Initialize Flutter binding for tests
      TestHelpers.setupIntegrationTestEnvironment();

      authService = AwsAuthService.instance;
      postsService = AwsPostsService.instance;

      // Try to authenticate as dev user (don't fail if it doesn't work)
      try {
        final authResponse = await authService.signIn(
          email: '<EMAIL>',
          password: 'DevPassword123!',
        );
        if (authResponse.success) {
          print('✅ <NAME_EMAIL>');
        } else {
          print('⚠️  Authentication failed: ${authResponse.message}');
        }
      } catch (e) {
        print('⚠️  Authentication failed: $e');
      }
    });

    test('Test AWS Backend Connection', () async {
      try {
        final posts = await postsService.getPosts(limit: 1);
        print(
          '✅ AWS backend connection successful. Retrieved ${posts.length} posts',
        );
        expect(posts, isA<List>());
      } catch (e) {
        print('⚠️  AWS backend connection failed: $e');
        print('💡 Make sure AWS backend is running: aws-backend/start.ps1');
        // Don't fail the test if backend is not available
      }
    });

    test('Test Authentication Status', () async {
      try {
        final isAuthenticated = authService.isAuthenticated;
        final currentUser = authService.currentUser;

        print('✅ Authentication check successful');
        print('Authenticated: $isAuthenticated');
        print('User: ${currentUser?.email}');

        if (!isAuthenticated || currentUser == null) {
          print('⚠️  Authentication check failed - user not authenticated');
          print('💡 This is expected if authentication failed in setUpAll');
          return; // Skip test if user is not authenticated
        }
      } catch (e) {
        print('⚠️  Authentication check failed: $e');
        print('💡 This is expected if backend is not running');
        // Don't fail the test if backend is not available
      }
    });

    test('Test Post Creation', () async {
      try {
        const testContent = 'Test post created by Flutter test (AWS Backend)';

        final createdPost = await postsService.createPost(content: testContent);

        if (createdPost != null) {
          print('✅ Post creation successful');
          print('Created post ID: ${createdPost.id}');
          print('Post content: ${createdPost.content}');

          expect(createdPost.id, isNotNull);
          expect(createdPost.content, equals(testContent));
          expect(createdPost.userId, isNotNull);
        } else {
          print('❌ Post creation returned null');
          fail('Post creation failed');
        }
      } catch (e) {
        print('⚠️  Post creation test skipped due to backend connectivity: $e');
        print('💡 Make sure AWS backend is running: aws-backend/start.ps1');
        // Don't fail the test if backend is not available
      }
    });
  });
}
