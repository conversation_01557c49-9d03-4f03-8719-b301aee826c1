import 'package:flutter_test/flutter_test.dart';
import 'package:gameflex_mobile/services/config_service.dart';
import 'package:flutter/foundation.dart';
import 'test_helpers.dart';

void main() {
  group('iOS Environment Configuration Tests (AWS Backend)', () {
    late ConfigService configService;

    setUpAll(() async {
      TestHelpers.setupTestEnvironment();
      configService = ConfigService.instance;
    });

    test('iOS should use localhost for development', () async {
      // This test runs in development mode by default (STAGING=false)
      final serverUrl = await configService.getServerUrl();

      // On iOS, should use localhost for development
      if (defaultTargetPlatform == TargetPlatform.iOS) {
        expect(
          serverUrl.contains('localhost') || serverUrl.contains('127.0.0.1'),
          true,
          reason: 'iOS development should use localhost URLs, got: $serverUrl',
        );

        // Should not use staging URL
        expect(
          serverUrl.contains('gameflex.io'),
          false,
          reason:
              'iOS development should not use staging URLs, got: $serverUrl',
        );
      }
    });

    test('iOS configuration should work based on environment', () async {
      const isStaging = bool.fromEnvironment('STAGING', defaultValue: false);

      final serverUrl = await configService.getServerUrl();
      final environment = configService.getEnvironmentName();

      if (isStaging) {
        // In staging, should use staging configuration
        expect(
          serverUrl.contains('dev.api.gameflex.io') || environment == 'Staging',
          true,
          reason: 'iOS should use staging configuration in staging mode',
        );
      } else {
        // In development on iOS, should use localhost
        expect(
          serverUrl.contains('localhost') || serverUrl.contains('127.0.0.1'),
          true,
          reason:
              'iOS should use localhost in development mode, got: $serverUrl',
        );

        // Should not contain staging URLs in development
        expect(
          serverUrl.contains('gameflex.io'),
          false,
          reason: 'iOS should not use staging URLs in development mode',
        );
      }
    });

    test('iOS environment detection should work correctly', () {
      const isStaging = bool.fromEnvironment('STAGING', defaultValue: false);

      // Verify the configuration reflects the current environment
      final environment = configService.getEnvironmentName();

      if (isStaging) {
        // In staging mode, should be staging environment
        expect(
          environment,
          equals('Staging'),
          reason: 'Should be in staging environment',
        );
      } else {
        // In development mode, should be development environment
        expect(
          environment,
          equals('Development'),
          reason: 'Should be in development environment',
        );
      }
    });
  });
}
