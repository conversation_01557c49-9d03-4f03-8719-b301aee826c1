import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:gameflex_mobile/services/config_service.dart';

void main() {
  group('LocalStack Configuration Tests', () {
    setUp(() async {
      // Clear any existing preferences
      SharedPreferences.setMockInitialValues({});
    });

    test('should default to LocalStack URLs in development', () async {
      final configService = ConfigService.instance;
      
      // Clear cache to ensure fresh detection
      configService.clearCache();
      
      // Get URLs
      final apiUrl = await configService.getServerUrl();
      final s3Url = await configService.getS3Url();
      
      // Should default to localhost LocalStack URLs
      expect(apiUrl, equals('http://127.0.0.1:4566'));
      expect(s3Url, equals('http://127.0.0.1:4566'));
    });

    test('should provide LocalStack options in dropdown', () {
      final configService = ConfigService.instance;
      final options = configService.getServerUrlOptions();
      
      // Should include LocalStack options
      final localhostOption = options.firstWhere(
        (option) => option.name == 'LocalStack (Localhost)',
        orElse: () => throw Exception('LocalStack localhost option not found'),
      );
      
      final emulatorOption = options.firstWhere(
        (option) => option.name == 'LocalStack (Android Emulator)',
        orElse: () => throw Exception('LocalStack emulator option not found'),
      );
      
      expect(localhostOption.url, equals('http://127.0.0.1:4566'));
      expect(emulatorOption.url, equals('http://********:4566'));
    });

    test('should map API URLs to corresponding S3 URLs', () async {
      final configService = ConfigService.instance;
      
      // Test LocalStack localhost mapping
      await configService.setServerUrl('http://127.0.0.1:4566', 'LocalStack (Localhost)');
      configService.clearCache();
      
      final apiUrl = await configService.getServerUrl();
      final s3Url = await configService.getS3Url();
      
      expect(apiUrl, equals('http://127.0.0.1:4566'));
      expect(s3Url, equals('http://127.0.0.1:4566'));
    });

    test('should handle Android emulator URLs correctly', () async {
      final configService = ConfigService.instance;
      
      // Test Android emulator mapping
      await configService.setServerUrl('http://********:4566', 'LocalStack (Android Emulator)');
      configService.clearCache();
      
      final apiUrl = await configService.getServerUrl();
      final s3Url = await configService.getS3Url();
      
      expect(apiUrl, equals('http://********:4566'));
      expect(s3Url, equals('http://********:4566'));
    });

    test('should include debug information for both API and S3 URLs', () async {
      final configService = ConfigService.instance;
      final debugInfo = await configService.getDebugInfo();
      
      expect(debugInfo.containsKey('currentServerUrl'), isTrue);
      expect(debugInfo.containsKey('currentS3Url'), isTrue);
      expect(debugInfo['environment'], equals('Development'));
    });
  });
}
