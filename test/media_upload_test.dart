import 'package:flutter_test/flutter_test.dart';
import 'package:gameflex_mobile/services/aws_auth_service.dart';
import 'package:gameflex_mobile/services/aws_posts_service.dart';
import 'test_helpers.dart';

void main() {
  group('Media Upload Tests (AWS Backend)', () {
    late AwsAuthService authService;
    late AwsPostsService postsService;

    setUpAll(() async {
      // Initialize Flutter binding for tests
      TestHelpers.setupIntegrationTestEnvironment();

      authService = AwsAuthService.instance;
      postsService = AwsPostsService.instance;
    });

    test('Test AWS backend post creation', () async {
      print('🧪 Testing AWS backend post creation...');

      try {
        // Authenticate as test user
        final authResponse = await authService.signIn(
          email: '<EMAIL>',
          password: 'DevPassword123!',
        );

        if (!authResponse.success) {
          print(
            '⚠️  Skipping test - authentication failed: ${authResponse.message}',
          );
          print(
            '💡 Make sure AWS backend is <NAME_EMAIL> user',
          );
          return; // Skip test if authentication fails
        }

        expect(authResponse.user, isNotNull);
        print('✅ Authenticated as: ${authResponse.user!.email}');

        // Test post creation through AWS backend
        const testContent = 'Test post with AWS backend';
        final createdPost = await postsService.createPost(content: testContent);

        expect(createdPost, isNotNull);
        expect(createdPost!.content, equals(testContent));
        print('✅ Post created successfully with AWS backend');

        // Verify post was created successfully
        expect(createdPost.id, isNotNull);
        expect(createdPost.userId, equals(authResponse.user!.id));
        print('✅ Post verified: ${createdPost.id}');

        // Test retrieving posts
        final posts = await postsService.getPosts(limit: 1);
        expect(posts, isNotEmpty);
        print('✅ Posts retrieved successfully: ${posts.length} posts');

        print('🎉 All AWS backend tests passed!');
      } catch (e) {
        print('⚠️  Test skipped due to backend connectivity: $e');
        print('💡 Make sure AWS backend is running: aws-backend/start.ps1');
        // Don't fail the test if backend is not available
      }
    });
  });
}
