{"name": "delete-test-01", "description": "delete-test-01-description", "version": "0.0.1", "private": true, "dependencies": {"@aws-sdk/client-dynamodb": "^3.398.0", "@aws-sdk/lib-dynamodb": "^3.398.0"}, "devDependencies": {"aws-sdk-client-mock": "^2.0.0", "jest": "^29.2.1"}, "scripts": {"test": "node --experimental-vm-modules ./node_modules/jest/bin/jest.js"}, "jest": {"testMatch": ["**/__tests__/**/*.[jt]s?(x)", "**/?(*.)+(spec|test).[jt]s?(x)", "**/__tests__/**/*.mjs?(x)", "**/?(*.)+(spec|test).mjs?(x)"], "moduleFileExtensions": ["mjs", "js"]}}