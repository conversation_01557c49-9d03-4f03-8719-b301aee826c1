{"name": "sam-starter-fontend", "version": "1.0.0", "description": "Frontend app for the Innovator Island workshop.", "author": "<PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "test": "vitest"}, "dependencies": {"axios": "^1.6.0", "core-js": "^3.18.1", "vue": "^3.0.0"}, "devDependencies": {"@testing-library/vue": "^7.0.0", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vitejs/plugin-vue": "^4.2.3", "@vue/cli-service": "^5.0.8", "@vue/compiler-sfc": "^3.0.0", "babel-eslint": "^10.1.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^7.18.0", "happy-dom": "^15.10.2", "vitest": "^0.31.1", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "prettier": {"semi": false, "singleQuote": true}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}