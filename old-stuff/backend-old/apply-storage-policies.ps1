#!/usr/bin/env pwsh
# Apply storage policies after storage service is ready

Write-Host "📦 Applying storage policies..." -ForegroundColor Yellow

# Wait for storage service to be healthy
$maxAttempts = 30
$attempt = 0
$storageReady = $false

while ($attempt -lt $maxAttempts -and -not $storageReady) {
    $attempt++
    Write-Host "   Checking storage service health (attempt $attempt/$maxAttempts)..." -ForegroundColor Gray
    
    try {
        $storageStatus = docker-compose ps storage --format json | ConvertFrom-Json
        if ($storageStatus.Health -eq "healthy") {
            $storageReady = $true
            Write-Host "✅ Storage service is healthy" -ForegroundColor Green
        }
        else {
            Write-Host "   Storage service status: $($storageStatus.Health)" -ForegroundColor Gray
            Start-Sleep -Seconds 2
        }
    }
    catch {
        Write-Host "   Storage service not ready yet..." -ForegroundColor Gray
        Start-Sleep -Seconds 2
    }
}

if (-not $storageReady) {
    Write-Host "⚠️  Storage service did not become healthy within timeout" -ForegroundColor Yellow
    Write-Host "   Skipping storage policies application" -ForegroundColor Gray
    return
}

# Apply storage policies
try {
    Write-Host "   Applying storage bucket and policies..." -ForegroundColor Gray

    # Create a temporary SQL file with the storage policies
    $tempSqlFile = [System.IO.Path]::GetTempFileName() + ".sql"

    $sqlContent = @"
-- Storage policies for media bucket
-- This file creates the necessary RLS policies for the media storage bucket

-- Wait for storage schema to be ready and create the media bucket if it doesn't exist
DO `$`$
BEGIN
    -- Check if storage.buckets table exists and has the expected columns
    IF EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_schema = 'storage' AND table_name = 'buckets'
    ) AND EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'storage' AND table_name = 'buckets' AND column_name = 'public'
    ) THEN
        -- Insert the media bucket
        INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types, avif_autodetection)
        VALUES (
            'media',
            'media',
            true,
            52428800, -- 50MB limit
            ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif', 'video/mp4', 'video/webm'],
            false
        )
        ON CONFLICT (id) DO UPDATE SET
            public = EXCLUDED.public,
            file_size_limit = EXCLUDED.file_size_limit,
            allowed_mime_types = EXCLUDED.allowed_mime_types,
            avif_autodetection = EXCLUDED.avif_autodetection;

        RAISE NOTICE 'Media bucket created/updated successfully';
    ELSE
        RAISE WARNING 'Storage schema not ready or missing expected columns, skipping bucket creation';
    END IF;
END `$`$;

-- Enable RLS on storage.objects if not already enabled
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Policy: Allow public read access to media files
CREATE POLICY "Media files are publicly accessible" ON storage.objects
    FOR SELECT USING (bucket_id = 'media');

-- Policy: Allow authenticated users to upload media files
CREATE POLICY "Authenticated users can upload media files" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'media'
        AND auth.role() = 'authenticated'
    );

-- Policy: Allow users to update their own media files
CREATE POLICY "Users can update their own media files" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'media'
        AND auth.uid()::text = (storage.foldername(name))[1]
    );

-- Policy: Allow users to delete their own media files
CREATE POLICY "Users can delete their own media files" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'media'
        AND auth.uid()::text = (storage.foldername(name))[1]
    );
"@

    # Write the SQL content to the temporary file
    Set-Content -Path $tempSqlFile -Value $sqlContent -Encoding UTF8

    # Copy the file to the container and execute it
    docker cp $tempSqlFile supabase-db:/tmp/storage_policies.sql | Out-Null
    $result = docker-compose exec -T db psql -U postgres -d postgres -f /tmp/storage_policies.sql 2>&1

    # Clean up the temporary file
    Remove-Item -Path $tempSqlFile -Force -ErrorAction SilentlyContinue

    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Storage policies applied successfully" -ForegroundColor Green
    }
    else {
        Write-Host "⚠️  Some storage policies may have failed to apply" -ForegroundColor Yellow
        Write-Host "   SQL output: $result" -ForegroundColor Gray
    }
}
catch {
    Write-Host "❌ Failed to apply storage policies: $($_.Exception.Message)" -ForegroundColor Red
}
