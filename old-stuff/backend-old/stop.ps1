# GameFlex Backend Stop Script (PowerShell)
# This script stops the Supabase development environment

param(
    [switch]$Help,
    [switch]$KeepData
)

if ($Help) {
    Write-Host "GameFlex Backend Stop Script" -ForegroundColor Green
    Write-Host "Usage: .\stop.ps1 [-KeepData]" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Cyan
    Write-Host "  -KeepData      Keep data volumes (by default all volumes are removed)" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Cyan
    Write-Host "  .\stop.ps1                # Stop services and remove all volumes (default)"
    Write-Host "  .\stop.ps1 -KeepData      # Stop services but keep data volumes"
    exit 0
}

# Set error action preference
$ErrorActionPreference = "Stop"

Write-Host "🛑 Stopping GameFlex Development Backend..." -ForegroundColor Red

# Check if Docker is running
try {
    docker info | Out-Null
} catch {
    Write-Host "❌ Docker is not running. Services may already be stopped." -ForegroundColor Yellow
    exit 0
}

# Check if docker-compose.yml exists
if (!(Test-Path "docker-compose.yml")) {
    Write-Host "❌ docker-compose.yml not found. Make sure you're in the backend directory." -ForegroundColor Red
    exit 1
}

try {
    if ($KeepData) {
        Write-Host "🐳 Stopping Docker containers (keeping data volumes)..." -ForegroundColor Yellow
        docker-compose down
        Write-Host "✅ All services stopped! Data volumes preserved." -ForegroundColor Green
    } else {
        Write-Host "🗑️  Stopping containers and removing all volumes..." -ForegroundColor Yellow
        docker-compose down -v
        Write-Host "✅ All services stopped and volumes removed!" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Error stopping services: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "You may need to stop containers manually:" -ForegroundColor Yellow
    Write-Host "  docker-compose down" -ForegroundColor White
    exit 1
}

# Show status
Write-Host ""
Write-Host "📊 Current Status:" -ForegroundColor Cyan
try {
    $containers = docker-compose ps --services 2>$null
    if ($containers) {
        Write-Host "   Running containers: " -NoNewline -ForegroundColor Gray
        $runningContainers = docker-compose ps --filter "status=running" --services 2>$null
        if ($runningContainers) {
            Write-Host "$($runningContainers.Count)" -ForegroundColor Yellow
        } else {
            Write-Host "0" -ForegroundColor Green
        }
    }
} catch {
    Write-Host "   All containers stopped" -ForegroundColor Green
}

Write-Host ""
Write-Host "🔧 Next Steps:" -ForegroundColor Magenta
Write-Host "   To start again: " -NoNewline -ForegroundColor Gray
Write-Host ".\start.ps1" -ForegroundColor White

if ($KeepData) {
    Write-Host "   To remove all data: " -NoNewline -ForegroundColor Gray
    Write-Host ".\stop.ps1" -ForegroundColor White
}

Write-Host "   To view logs: " -NoNewline -ForegroundColor Gray
Write-Host "docker-compose logs" -ForegroundColor White
Write-Host ""
