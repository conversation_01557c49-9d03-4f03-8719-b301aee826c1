#!/bin/bash

# GameFlex Backend Stop Script (Bash)
# This script stops the Supabase development environment

# Function to show help
show_help() {
    echo -e "\033[32mGameFlex Backend Stop Script\033[0m"
    echo -e "\033[33mUsage: ./stop.sh [OPTIONS]\033[0m"
    echo ""
    echo -e "\033[36mOptions:\033[0m"
    echo -e "\033[33m  --keep-data      Keep data volumes (by default all volumes are removed)\033[0m"
    echo -e "\033[33m  --help          Show this help message\033[0m"
    echo ""
    echo -e "\033[36mExamples:\033[0m"
    echo "  ./stop.sh                    # Stop services and remove all volumes (default)"
    echo "  ./stop.sh --keep-data        # Stop services but keep data volumes"
}

# Parse command line arguments
KEEP_DATA=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --keep-data)
            KEEP_DATA=true
            shift
            ;;
        --help|-h)
            show_help
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

set -e

echo -e "\033[31m🛑 Stopping GameFlex Development Backend...\033[0m"

# Check if Docker is running
if docker info > /dev/null 2>&1; then
    # Docker is running, continue
    :
else
    echo -e "\033[33m❌ Docker is not running. Services may already be stopped.\033[0m"
    exit 0
fi

# Check if docker-compose.yml exists
if [ ! -f "docker-compose.yml" ]; then
    echo -e "\033[31m❌ docker-compose.yml not found. Make sure you're in the backend directory.\033[0m"
    exit 1
fi

# Determine Docker Compose command (prefer new syntax)
if command -v docker > /dev/null 2>&1 && docker compose version > /dev/null 2>&1; then
    DOCKER_COMPOSE_CMD="docker compose"
elif command -v docker-compose > /dev/null 2>&1; then
    DOCKER_COMPOSE_CMD="docker-compose"
else
    echo -e "\033[31m❌ Docker Compose is not available.\033[0m"
    exit 1
fi

if [ "$KEEP_DATA" = true ]; then
    echo -e "\033[33m🐳 Stopping Docker containers (keeping data volumes)...\033[0m"
    if $DOCKER_COMPOSE_CMD down; then
        echo -e "\033[32m✅ All services stopped! Data volumes preserved.\033[0m"
    else
        echo -e "\033[31m❌ Error stopping services\033[0m"
        echo -e "\033[33mYou may need to stop containers manually:\033[0m"
        echo -e "\033[37m  $DOCKER_COMPOSE_CMD down\033[0m"
        exit 1
    fi
else
    echo -e "\033[33m🗑️  Stopping containers and removing all volumes...\033[0m"
    if $DOCKER_COMPOSE_CMD down -v; then
        echo -e "\033[32m✅ All services stopped and volumes removed!\033[0m"
    else
        echo -e "\033[31m❌ Error stopping services\033[0m"
        echo -e "\033[33mYou may need to stop containers manually:\033[0m"
        echo -e "\033[37m  $DOCKER_COMPOSE_CMD down\033[0m"
        exit 1
    fi
fi

# Show status
echo ""
echo -e "\033[36m📊 Current Status:\033[0m"
containers=$($DOCKER_COMPOSE_CMD ps --services 2>/dev/null || echo "")
if [ -n "$containers" ]; then
    echo -n -e "\033[37m   Running containers: \033[0m"
    running_containers=$($DOCKER_COMPOSE_CMD ps --filter "status=running" --services 2>/dev/null || echo "")
    if [ -n "$running_containers" ]; then
        running_count=$(echo "$running_containers" | wc -l)
        echo -e "\033[33m$running_count\033[0m"
    else
        echo -e "\033[32m0\033[0m"
    fi
else
    echo -e "\033[32m   All containers stopped\033[0m"
fi

echo ""
echo -e "\033[35m🔧 Next Steps:\033[0m"
echo -e "\033[37m   To start again: \033[37m./start.sh\033[0m"

if [ "$KEEP_DATA" = true ]; then
    echo -e "\033[37m   To remove all data: \033[37m./stop.sh\033[0m"
fi

echo -e "\033[37m   To view logs: \033[37m$DOCKER_COMPOSE_CMD logs\033[0m"
echo ""
