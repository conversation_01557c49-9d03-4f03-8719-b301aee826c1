/**
 * Debug script to show what URLs the integration tests are trying to access
 */

// Set environment variables like <PERSON><PERSON> does
process.env.AWS_ENDPOINT_URL = 'http://localhost:45660';
process.env.AWS_ACCESS_KEY_ID = 'test';
process.env.AWS_SECRET_ACCESS_KEY = 'test';
process.env.AWS_DEFAULT_REGION = 'us-east-1';

// Get the API Gateway URL from CloudFormation (like global setup does)
const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);

async function getApiGatewayUrl() {
  try {
    console.log('🔍 Getting API Gateway URL from CloudFormation...');
    const cfCommand = `aws --endpoint-url=http://localhost:45660 cloudformation describe-stacks --stack-name gameflex-infrastructure-development --query "Stacks[0].Outputs[?OutputKey=='ApiGatewayUrl'].OutputValue" --output text --region us-east-1`;
    const { stdout } = await execAsync(cfCommand);
    const apiGatewayUrl = stdout.trim();

    if (apiGatewayUrl && apiGatewayUrl !== 'None' && apiGatewayUrl !== '') {
      console.log(`📡 Found API Gateway URL: ${apiGatewayUrl}`);
      return apiGatewayUrl;
    }

    throw new Error('No API Gateway URL found in CloudFormation outputs');
  } catch (error) {
    console.error('❌ Failed to get API Gateway URL:', error.message);
    return null;
  }
}

async function main() {
  console.log('🚀 Debug: Integration Test URLs');
  console.log('================================');

  const baseUrl = await getApiGatewayUrl();

  if (!baseUrl) {
    console.log('❌ Cannot determine base URL');
    return;
  }

  console.log('\n📋 URLs that integration tests are trying to access:');
  console.log('====================================================');

  // auth-integration.test.ts URLs
  console.log('\n🔐 Auth Integration Tests:');
  console.log(`   POST ${baseUrl}/auth/signup`);
  console.log(`   POST ${baseUrl}/auth/signin`);
  console.log(`   GET  ${baseUrl}/auth/profile`);
  console.log(`   PUT  ${baseUrl}/auth/profile`);
  console.log(`   POST ${baseUrl}/auth/signout`);
  console.log(`   POST ${baseUrl}/auth/refresh`);
  console.log(`   POST ${baseUrl}/auth/forgot-password`);
  console.log(`   POST ${baseUrl}/auth/reset-password`);

  // auth-flow.test.ts - these are direct Lambda handler calls, not HTTP requests
  console.log('\n🔧 Auth Flow Tests (Direct Lambda Handler Calls):');
  console.log('   ⚠️  These tests call the Lambda handler directly, not via HTTP');
  console.log('   ⚠️  They do not use the API Gateway URLs above');

  console.log('\n🧪 Testing actual API Gateway connectivity:');
  console.log('===========================================');

  // Test the signup endpoint
  const testUrl = `${baseUrl}/auth/signup`;
  console.log(`\n🌐 Testing: POST ${testUrl}`);

  try {
    const response = await fetch(testUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'DebugPassword123!',
        username: 'debuguser'
      })
    });

    console.log(`📥 Response Status: ${response.status} ${response.statusText}`);
    const responseText = await response.text();
    console.log(`📦 Response Body: ${responseText}`);

  } catch (error) {
    console.error(`❌ Request failed: ${error.message}`);
  }
}

main().catch(console.error);
