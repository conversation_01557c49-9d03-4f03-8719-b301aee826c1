# Deployer Docker Image
# This image contains AWS CLI and tools needed for CloudFormation deployment
FROM alpine:3.18

# Install system dependencies
RUN apk add --no-cache \
    aws-cli \
    bash \
    curl \
    jq \
    zip \
    unzip \
    nodejs \
    npm \
    git

# Create working directory
WORKDIR /workspace

# Create directories for scripts and packages
RUN mkdir -p /usr/local/bin /workspace/packages /workspace/cloudformation /workspace/init

# Set proper permissions
RUN chmod 755 /usr/local/bin

# Set default command (will be overridden by docker-compose)
CMD ["bash", "/usr/local/bin/init-gameflex.sh"]
