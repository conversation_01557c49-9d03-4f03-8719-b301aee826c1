#!/bin/bash

# GameFlex AWS Infrastructure Deployment Script
# This script deploys the complete AWS infrastructure using CloudFormation

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${BLUE}[DEPLOY]${NC} $1"
}

# Set default values
STACK_NAME="${STACK_NAME:-gameflex-infrastructure-development}"
TEMPLATE_FILE="${TEMPLATE_FILE:-/workspace/cloudformation/gameflex-infrastructure.yaml}"
PARAMETERS_FILE="${PARAMETERS_FILE:-/workspace/cloudformation/parameters/development.json}"
AWS_ENDPOINT_URL="${AWS_ENDPOINT_URL:-http://localstack:4566}"

# Change to workspace directory
cd /workspace

log_header "Deploying GameFlex AWS Infrastructure"
log_info "Stack Name: $STACK_NAME"
log_info "Template: $TEMPLATE_FILE"
log_info "Parameters: $PARAMETERS_FILE"
log_info "Endpoint: $AWS_ENDPOINT_URL"

# Check if template file exists
if [ ! -f "$TEMPLATE_FILE" ]; then
    log_error "Template file not found: $TEMPLATE_FILE"
    exit 1
fi

# Check if parameters file exists
if [ ! -f "$PARAMETERS_FILE" ]; then
    log_error "Parameters file not found: $PARAMETERS_FILE"
    exit 1
fi

# Wait for LocalStack to be ready
log_info "Waiting for LocalStack to be ready..."
timeout=120
counter=0
while [ $counter -lt $timeout ]; do
    if aws --endpoint-url="$AWS_ENDPOINT_URL" sts get-caller-identity > /dev/null 2>&1; then
        log_info "LocalStack is ready"
        break
    fi
    sleep 2
    counter=$((counter + 2))
    if [ $counter -ge $timeout ]; then
        log_error "LocalStack not ready within $timeout seconds"
        exit 1
    fi
done

# Function to clean up existing resources that might conflict
cleanup_existing_resources() {
    log_info "Cleaning up existing resources that might conflict..."
    
    # Clean up Lambda functions
    local lambda_functions=(
        "gameflex-auth-development"
        "gameflex-posts-development"
        "gameflex-media-development"
        "gameflex-users-development"
    )
    
    for func in "${lambda_functions[@]}"; do
        if aws --endpoint-url="$AWS_ENDPOINT_URL" lambda get-function --function-name "$func" > /dev/null 2>&1; then
            log_info "Deleting existing Lambda function: $func"
            aws --endpoint-url="$AWS_ENDPOINT_URL" lambda delete-function --function-name "$func" || true
        fi
    done
    
    # Clean up IAM roles
    local iam_roles=(
        "gameflex-lambda-execution-role-development"
        "gameflex-authenticated-role-development"
    )
    
    for role in "${iam_roles[@]}"; do
        if aws --endpoint-url="$AWS_ENDPOINT_URL" iam get-role --role-name "$role" > /dev/null 2>&1; then
            log_info "Deleting existing IAM role: $role"
            
            # Detach managed policies
            aws --endpoint-url="$AWS_ENDPOINT_URL" iam list-attached-role-policies --role-name "$role" --query "AttachedPolicies[].PolicyArn" --output text | tr '\t' '\n' | while read -r policy_arn; do
                if [ -n "$policy_arn" ]; then
                    aws --endpoint-url="$AWS_ENDPOINT_URL" iam detach-role-policy --role-name "$role" --policy-arn "$policy_arn" || true
                fi
            done
            
            # Delete inline policies
            aws --endpoint-url="$AWS_ENDPOINT_URL" iam list-role-policies --role-name "$role" --query "PolicyNames" --output text | tr '\t' '\n' | while read -r policy_name; do
                if [ -n "$policy_name" ]; then
                    aws --endpoint-url="$AWS_ENDPOINT_URL" iam delete-role-policy --role-name "$role" --policy-name "$policy_name" || true
                fi
            done
            
            # Delete role
            aws --endpoint-url="$AWS_ENDPOINT_URL" iam delete-role --role-name "$role" || true
        fi
    done
}

# Function to upload Lambda packages to S3
upload_lambda_packages() {
    log_info "Uploading Lambda packages to S3..."
    
    # Create S3 bucket for Lambda code
    local bucket_name="gameflex-lambda-code-development"
    aws --endpoint-url="$AWS_ENDPOINT_URL" s3 mb "s3://$bucket_name" 2>/dev/null || true
    
    # Check if packages exist
    if [ ! -d "/workspace/packages" ] || [ -z "$(ls -A /workspace/packages/*.zip 2>/dev/null)" ]; then
        log_error "No Lambda packages found in /workspace/packages/"
        log_error "Please build Lambda functions first"
        exit 1
    fi
    
    # Upload Lambda packages
    for package in /workspace/packages/*.zip; do
        if [ -f "$package" ]; then
            local package_name=$(basename "$package")
            log_info "Uploading $package_name..."
            aws --endpoint-url="$AWS_ENDPOINT_URL" s3 cp "$package" "s3://$bucket_name/$package_name"
        fi
    done
    
    log_info "Lambda packages uploaded successfully"
}

# Function to handle stack deployment
deploy_stack() {
    # Check if stack already exists
    log_info "Checking if stack already exists: $STACK_NAME"
    local stack_status=$(aws --endpoint-url="$AWS_ENDPOINT_URL" cloudformation describe-stacks --stack-name "$STACK_NAME" --query "Stacks[0].StackStatus" --output text 2>/dev/null || echo "DOES_NOT_EXIST")
    
    if [ "$stack_status" != "DOES_NOT_EXIST" ]; then
        log_warn "Stack already exists with status: $stack_status"
        
        # Handle different stack states
        case "$stack_status" in
            "CREATE_FAILED"|"ROLLBACK_COMPLETE"|"DELETE_FAILED")
                log_info "Deleting failed stack..."
                aws --endpoint-url="$AWS_ENDPOINT_URL" cloudformation delete-stack --stack-name "$STACK_NAME"
                
                log_info "Waiting for stack deletion..."
                aws --endpoint-url="$AWS_ENDPOINT_URL" cloudformation wait stack-delete-complete --stack-name "$STACK_NAME" --cli-read-timeout 300
                ;;
            "CREATE_COMPLETE"|"UPDATE_COMPLETE")
                log_info "Updating existing stack..."
                aws --endpoint-url="$AWS_ENDPOINT_URL" cloudformation update-stack \
                    --stack-name "$STACK_NAME" \
                    --template-body "file://$TEMPLATE_FILE" \
                    --parameters "file://$PARAMETERS_FILE" \
                    --capabilities CAPABILITY_IAM
                
                log_info "Waiting for stack update to complete..."
                aws --endpoint-url="$AWS_ENDPOINT_URL" cloudformation wait stack-update-complete --stack-name "$STACK_NAME" --cli-read-timeout 300
                
                log_header "Stack updated successfully"
                return 0
                ;;
            *)
                log_error "Stack is in unexpected state: $stack_status"
                exit 1
                ;;
        esac
    fi
    
    # Create new stack
    log_info "Creating new CloudFormation stack: $STACK_NAME"
    aws --endpoint-url="$AWS_ENDPOINT_URL" cloudformation create-stack \
        --stack-name "$STACK_NAME" \
        --template-body "file://$TEMPLATE_FILE" \
        --parameters "file://$PARAMETERS_FILE" \
        --capabilities CAPABILITY_IAM
    
    # Wait for stack creation to complete
    log_info "Waiting for stack creation to complete..."
    aws --endpoint-url="$AWS_ENDPOINT_URL" cloudformation wait stack-create-complete --stack-name "$STACK_NAME" --cli-read-timeout 300
    
    if [ $? -eq 0 ]; then
        log_header "Stack created successfully"
    else
        log_error "Stack creation failed"
        
        # Show stack events for debugging
        log_info "Recent stack events:"
        aws --endpoint-url="$AWS_ENDPOINT_URL" cloudformation describe-stack-events --stack-name "$STACK_NAME" --query "StackEvents[?ResourceStatus=='CREATE_FAILED'].[LogicalResourceId,ResourceStatusReason]" --output table
        
        exit 1
    fi
}

# Function to display stack outputs
show_stack_outputs() {
    log_info "Stack outputs:"
    aws --endpoint-url="$AWS_ENDPOINT_URL" cloudformation describe-stacks --stack-name "$STACK_NAME" --query "Stacks[0].Outputs" --output table 2>/dev/null || log_warn "Could not retrieve stack outputs"
}

# Main execution
cleanup_existing_resources
upload_lambda_packages
deploy_stack
show_stack_outputs

log_header "Deployment completed successfully"
