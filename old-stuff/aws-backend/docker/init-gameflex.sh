#!/bin/bash

# GameFlex Complete Initialization Script
# This script orchestrates the complete setup of the GameFlex AWS backend

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${BLUE}[GAMEFLEX-INIT]${NC} $1"
}

# Configuration
AWS_ENDPOINT_URL="${AWS_ENDPOINT_URL:-http://localstack:4566}"
ENVIRONMENT="${ENVIRONMENT:-development}"
PROJECT_NAME="${PROJECT_NAME:-gameflex}"

log_header "Starting GameFlex AWS Backend Initialization"
log_info "Environment: $ENVIRONMENT"
log_info "Project: $PROJECT_NAME"
log_info "AWS Endpoint: $AWS_ENDPOINT_URL"

# Function to wait for LocalStack to be ready
wait_for_localstack() {
    log_info "Waiting for LocalStack to be ready..."
    local timeout=120
    local counter=0
    
    while [ $counter -lt $timeout ]; do
        if aws --endpoint-url="$AWS_ENDPOINT_URL" sts get-caller-identity > /dev/null 2>&1; then
            log_info "LocalStack is ready"
            return 0
        fi
        sleep 2
        counter=$((counter + 2))
        if [ $counter -ge $timeout ]; then
            log_error "LocalStack not ready within $timeout seconds"
            return 1
        fi
    done
}

# Function to build Lambda functions
build_lambda_functions() {
    log_header "Building Lambda Functions"
    
    # Check if build script exists
    if [ ! -f "/usr/local/bin/build-all-lambdas.sh" ]; then
        log_error "Lambda build script not found"
        return 1
    fi
    
    # Make sure the script is executable
    chmod +x /usr/local/bin/build-all-lambdas.sh
    
    # Run the build script
    /usr/local/bin/build-all-lambdas.sh
    
    if [ $? -eq 0 ]; then
        log_info "Lambda functions built successfully"
        return 0
    else
        log_error "Lambda function build failed"
        return 1
    fi
}

# Function to run basic AWS service initialization
init_basic_services() {
    log_header "Initializing Basic AWS Services"
    
    # Run the existing initialization scripts in order
    local init_scripts=(
        "/workspace/init/00-init-cognito-users.sh"
        "/workspace/init/01-create-dynamodb-tables.sh"
        "/workspace/init/02-seed-dynamodb-data.sh"
        "/workspace/init/03-create-s3-buckets.sh"
    )
    
    for script in "${init_scripts[@]}"; do
        if [ -f "$script" ]; then
            log_info "Running $(basename "$script")..."
            chmod +x "$script"
            "$script"
            if [ $? -eq 0 ]; then
                log_info "✓ $(basename "$script") completed successfully"
            else
                log_error "✗ $(basename "$script") failed"
                return 1
            fi
        else
            log_warn "Script not found: $script"
        fi
    done
    
    log_info "Basic AWS services initialized successfully"
}

# Function to deploy infrastructure
deploy_infrastructure() {
    log_header "Deploying CloudFormation Infrastructure"
    
    # Check if deployment script exists
    if [ ! -f "/usr/local/bin/deploy-infrastructure.sh" ]; then
        log_error "Infrastructure deployment script not found"
        return 1
    fi
    
    # Make sure the script is executable
    chmod +x /usr/local/bin/deploy-infrastructure.sh
    
    # Run the deployment script
    /usr/local/bin/deploy-infrastructure.sh
    
    if [ $? -eq 0 ]; then
        log_info "Infrastructure deployed successfully"
        return 0
    else
        log_error "Infrastructure deployment failed"
        return 1
    fi
}

# Function to verify deployment
verify_deployment() {
    log_header "Verifying Deployment"
    
    # Check if stack exists and is in good state
    local stack_status=$(aws --endpoint-url="$AWS_ENDPOINT_URL" cloudformation describe-stacks --stack-name "gameflex-infrastructure-development" --query "Stacks[0].StackStatus" --output text 2>/dev/null || echo "NOT_FOUND")
    
    if [ "$stack_status" = "CREATE_COMPLETE" ] || [ "$stack_status" = "UPDATE_COMPLETE" ]; then
        log_info "✓ CloudFormation stack is healthy: $stack_status"
    else
        log_error "✗ CloudFormation stack is not healthy: $stack_status"
        return 1
    fi
    
    # Check Lambda functions
    local lambda_functions=(
        "gameflex-auth-development"
        "gameflex-posts-development"
        "gameflex-media-development"
        "gameflex-users-development"
    )
    
    local lambda_count=0
    for func in "${lambda_functions[@]}"; do
        if aws --endpoint-url="$AWS_ENDPOINT_URL" lambda get-function --function-name "$func" > /dev/null 2>&1; then
            log_info "✓ Lambda function exists: $func"
            lambda_count=$((lambda_count + 1))
        else
            log_warn "✗ Lambda function not found: $func"
        fi
    done
    
    log_info "Lambda functions deployed: $lambda_count/${#lambda_functions[@]}"
    
    # Check DynamoDB tables
    local tables=$(aws --endpoint-url="$AWS_ENDPOINT_URL" dynamodb list-tables --query "TableNames" --output text 2>/dev/null || echo "")
    local table_count=$(echo "$tables" | wc -w)
    log_info "DynamoDB tables created: $table_count"
    
    # Check S3 buckets
    local buckets=$(aws --endpoint-url="$AWS_ENDPOINT_URL" s3 ls 2>/dev/null | wc -l || echo "0")
    log_info "S3 buckets created: $buckets"
    
    # Check Cognito User Pool
    local user_pools=$(aws --endpoint-url="$AWS_ENDPOINT_URL" cognito-idp list-user-pools --max-results 50 --query "UserPools" --output text 2>/dev/null | wc -l || echo "0")
    log_info "Cognito User Pools created: $user_pools"
    
    if [ $lambda_count -eq ${#lambda_functions[@]} ] && [ $table_count -gt 0 ] && [ $buckets -gt 0 ] && [ $user_pools -gt 0 ]; then
        log_info "✓ All services appear to be deployed correctly"
        return 0
    else
        log_warn "Some services may not be deployed correctly"
        return 1
    fi
}

# Function to display service information
show_service_info() {
    log_header "GameFlex AWS Backend Deployment Complete"
    
    # Get API Gateway URL if available
    local api_url=$(aws --endpoint-url="$AWS_ENDPOINT_URL" cloudformation describe-stacks --stack-name "gameflex-infrastructure-development" --query "Stacks[0].Outputs[?OutputKey=='ApiGatewayUrl'].OutputValue" --output text 2>/dev/null || echo "")
    
    if [ -n "$api_url" ]; then
        log_info "API Gateway URL: $api_url"
    fi
    
    log_info "Service URLs:"
    echo "  LocalStack Health: $AWS_ENDPOINT_URL/_localstack/health"
    echo "  DynamoDB: $AWS_ENDPOINT_URL"
    echo "  S3 Console: $AWS_ENDPOINT_URL/_aws/s3"
    echo "  Cognito Console: $AWS_ENDPOINT_URL/_aws/cognito"
    
    log_info "Development Credentials:"
    echo "  Developer: <EMAIL> / DevPassword123!"
    echo "  Admin: <EMAIL> / AdminPassword123!"
    
    log_info "Useful Commands:"
    echo "  Check stack: aws --endpoint-url=$AWS_ENDPOINT_URL cloudformation describe-stacks --stack-name gameflex-infrastructure-development"
    echo "  List Lambda functions: aws --endpoint-url=$AWS_ENDPOINT_URL lambda list-functions"
    echo "  List DynamoDB tables: aws --endpoint-url=$AWS_ENDPOINT_URL dynamodb list-tables"
}

# Main execution flow
main() {
    # Step 1: Wait for LocalStack
    if ! wait_for_localstack; then
        log_error "Failed to connect to LocalStack"
        exit 1
    fi
    
    # Step 2: Build Lambda functions
    if ! build_lambda_functions; then
        log_error "Failed to build Lambda functions"
        exit 1
    fi
    
    # Step 3: Initialize basic services
    if ! init_basic_services; then
        log_error "Failed to initialize basic services"
        exit 1
    fi
    
    # Step 4: Deploy infrastructure
    if ! deploy_infrastructure; then
        log_error "Failed to deploy infrastructure"
        exit 1
    fi
    
    # Step 5: Verify deployment
    if ! verify_deployment; then
        log_warn "Deployment verification failed, but continuing..."
    fi
    
    # Step 6: Show service information
    show_service_info
    
    log_header "GameFlex AWS Backend initialization completed successfully!"
}

# Run main function
main "$@"
