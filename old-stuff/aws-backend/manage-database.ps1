# GameFlex DynamoDB Management Script (PowerShell)
# This script provides DynamoDB table management utilities for the AWS backend

param(
    [string]$Action = "status",
    [string]$EndpointUrl = "http://localhost:4566",
    [switch]$Force,
    [switch]$Verbose
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Header {
    param([string]$Message)
    Write-Host "[DYNAMODB] $Message" -ForegroundColor Blue
}

# Test DynamoDB connection
function Test-DynamoDBConnection {
    try {
        # Set AWS credentials for LocalStack
        $env:AWS_ACCESS_KEY_ID = "test"
        $env:AWS_SECRET_ACCESS_KEY = "test"
        $env:AWS_DEFAULT_REGION = "us-east-1"

        $result = aws --endpoint-url=$EndpointUrl dynamodb list-tables 2>$null

        if ($LASTEXITCODE -eq 0) {
            Write-Status "DynamoDB connection successful"
            return $true
        }
        else {
            Write-Error "DynamoDB connection failed"
            return $false
        }
    }
    catch {
        Write-Error "Failed to test DynamoDB connection: $_"
        return $false
    }
    finally {
        Remove-Item Env:AWS_ACCESS_KEY_ID -ErrorAction SilentlyContinue
        Remove-Item Env:AWS_SECRET_ACCESS_KEY -ErrorAction SilentlyContinue
        Remove-Item Env:AWS_DEFAULT_REGION -ErrorAction SilentlyContinue
    }
}

# Get DynamoDB status
function Get-DatabaseStatus {
    try {
        Write-Header "DynamoDB Status"
        
        # Connection test
        if (-not (Test-DynamoDBConnection)) {
            return $false
        }
        
        Write-Status "DynamoDB connection: OK"
        Write-Status "Endpoint: $EndpointUrl"
        Write-Host ""
        
        # Set AWS credentials for operations
        $env:AWS_ACCESS_KEY_ID = "test"
        $env:AWS_SECRET_ACCESS_KEY = "test"
        $env:AWS_DEFAULT_REGION = "us-east-1"
        
        # List tables
        Write-Status "DynamoDB Tables:"
        $tablesResult = aws --endpoint-url=$EndpointUrl dynamodb list-tables --output json 2>$null
        if ($LASTEXITCODE -eq 0) {
            $tables = ($tablesResult | ConvertFrom-Json).TableNames
            foreach ($table in $tables) {
                Write-Host "  ✓ $table" -ForegroundColor Cyan
                
                # Get item count for each table
                try {
                    $scanResult = aws --endpoint-url=$EndpointUrl dynamodb scan --table-name $table --select COUNT --output json 2>$null
                    if ($LASTEXITCODE -eq 0) {
                        $count = ($scanResult | ConvertFrom-Json).Count
                        Write-Host "    Items: $count" -ForegroundColor Gray
                    }
                }
                catch {
                    Write-Host "    Items: Unable to count" -ForegroundColor Yellow
                }
            }
        }
        else {
            Write-Warning "Could not list tables"
        }
        
        Write-Host ""
        
        # Recent activity - sample from Posts table if it exists
        if ($tables -contains "Posts") {
            Write-Status "Recent Posts (Sample):"
            try {
                $scanResult = aws --endpoint-url=$EndpointUrl dynamodb scan --table-name Posts --limit 5 --output json 2>$null
                if ($LASTEXITCODE -eq 0) {
                    $items = ($scanResult | ConvertFrom-Json).Items
                    foreach ($item in $items) {
                        $content = if ($item.content -and $item.content.S) { $item.content.S } else { "No content" }
                        $createdAt = if ($item.created_at -and $item.created_at.S) { $item.created_at.S } else { "Unknown" }
                        Write-Host "  - $($content.Substring(0, [Math]::Min(50, $content.Length)))... ($createdAt)" -ForegroundColor Cyan
                    }
                }
            }
            catch {
                Write-Warning "Could not retrieve recent posts"
            }
        }
        
        return $true
    }
    catch {
        Write-Error "Failed to get DynamoDB status: $_"
        return $false
    }
    finally {
        Remove-Item Env:AWS_ACCESS_KEY_ID -ErrorAction SilentlyContinue
        Remove-Item Env:AWS_SECRET_ACCESS_KEY -ErrorAction SilentlyContinue
        Remove-Item Env:AWS_DEFAULT_REGION -ErrorAction SilentlyContinue
    }
}

# Reset DynamoDB tables
function Reset-Database {
    if (-not $Force) {
        $response = Read-Host "This will delete ALL DynamoDB tables and recreate them. Are you sure? (type 'yes' to confirm)"
        if ($response -ne "yes") {
            Write-Status "DynamoDB reset cancelled"
            return $true
        }
    }
    
    try {
        Write-Header "Resetting DynamoDB Tables"
        
        # Set AWS credentials
        $env:AWS_ACCESS_KEY_ID = "test"
        $env:AWS_SECRET_ACCESS_KEY = "test"
        $env:AWS_DEFAULT_REGION = "us-east-1"
        
        # Get list of existing tables
        Write-Status "Getting list of existing tables..."
        $tablesResult = aws --endpoint-url=$EndpointUrl dynamodb list-tables --output json 2>$null
        if ($LASTEXITCODE -eq 0) {
            $tables = ($tablesResult | ConvertFrom-Json).TableNames
            
            # Delete existing tables
            foreach ($table in $tables) {
                Write-Status "Deleting table: $table"
                aws --endpoint-url=$EndpointUrl dynamodb delete-table --table-name $table 2>$null
                if ($LASTEXITCODE -eq 0) {
                    Write-Status "Deleted table: $table"
                }
                else {
                    Write-Warning "Failed to delete table: $table"
                }
            }
        }
        
        # Wait a moment for deletions to complete
        Start-Sleep -Seconds 2
        
        # Recreate tables using initialization script
        Write-Status "Recreating DynamoDB tables..."
        if (Test-Path "init\01-create-dynamodb-tables.sh") {
            # Run the table creation script
            bash "init\01-create-dynamodb-tables.sh"
            if ($LASTEXITCODE -eq 0) {
                Write-Status "Tables created successfully"
            }
            else {
                Write-Error "Failed to create tables"
                return $false
            }
        }
        else {
            Write-Warning "Table creation script not found: init\01-create-dynamodb-tables.sh"
        }
        
        # Seed data using initialization script
        Write-Status "Seeding DynamoDB tables..."
        if (Test-Path "init\02-seed-dynamodb-data.sh") {
            bash "init\02-seed-dynamodb-data.sh"
            if ($LASTEXITCODE -eq 0) {
                Write-Status "Data seeded successfully"
            }
            else {
                Write-Warning "Failed to seed data"
            }
        }
        else {
            Write-Warning "Data seeding script not found: init\02-seed-dynamodb-data.sh"
        }
        
        Write-Status "DynamoDB reset completed!"
        return $true
    }
    catch {
        Write-Error "Failed to reset DynamoDB: $_"
        return $false
    }
    finally {
        Remove-Item Env:AWS_ACCESS_KEY_ID -ErrorAction SilentlyContinue
        Remove-Item Env:AWS_SECRET_ACCESS_KEY -ErrorAction SilentlyContinue
        Remove-Item Env:AWS_DEFAULT_REGION -ErrorAction SilentlyContinue
    }
}

# Main execution
function Main {
    Write-Header "GameFlex DynamoDB Management"
    Write-Host ""

    switch ($Action.ToLower()) {
        "status" {
            Get-DatabaseStatus
        }
        "reset" {
            Reset-Database
        }
        default {
            Write-Error "Unknown action: $Action"
            Write-Host ""
            Write-Host "Available actions:" -ForegroundColor Yellow
            Write-Host "  status    - Show DynamoDB status and table information"
            Write-Host "  reset     - Reset all DynamoDB tables (delete and recreate)"
            Write-Host ""
            Write-Host "Options:" -ForegroundColor Yellow
            Write-Host "  -EndpointUrl  - DynamoDB endpoint URL (default: http://localhost:4566)"
            Write-Host "  -Force        - Skip confirmation prompts"
            Write-Host "  -Verbose      - Show verbose output"
            exit 1
        }
    }
}

# Run main function
try {
    Main
}
catch {
    Write-Error "Script failed: $_"
    exit 1
}
