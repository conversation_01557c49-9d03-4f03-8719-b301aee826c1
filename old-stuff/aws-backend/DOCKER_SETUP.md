# GameFlex AWS Backend - Docker-based Setup

This document describes the improved Docker-based setup for the GameFlex AWS backend using LocalStack Pro.

## Overview

The new setup uses Docker containers to handle all aspects of the AWS backend initialization:

- **Lambda Builder Container**: Compiles TypeScript Lambda functions and creates deployment packages
- **AWS Initializer Container**: Deploys CloudFormation infrastructure and initializes AWS services
- **LocalStack Pro Container**: Provides AWS service emulation for development

## Architecture

```
┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
│   Lambda Builder    │    │  AWS Initializer    │    │    LocalStack Pro   │
│                     │    │                     │    │                     │
│ - Builds TypeScript │    │ - Deploys CloudForm │    │ - AWS Services      │
│ - Creates packages  │    │ - Initializes AWS   │    │ - DynamoDB, S3, etc │
│ - Node.js + TypeScript│  │ - Runs init scripts │    │ - Cognito, Lambda   │
└─────────────────────┘    └─────────────────────┘    └─────────────────────┘
```

## Quick Start

### Prerequisites

- Windows 11 with Docker Desktop
- PowerShell 5.1 or later
- LocalStack Pro auth token (configured in `.env`)

### Basic Usage

1. **Start the backend** (builds and initializes everything):
   ```powershell
   .\start.ps1
   ```

2. **Stop the backend**:
   ```powershell
   .\stop.ps1
   ```

3. **Test the setup**:
   ```powershell
   .\test-docker-setup.ps1
   ```

### Advanced Usage

- **Build Lambda functions only**:
  ```powershell
  .\start.ps1 -BuildOnly
  ```

- **Skip Lambda build** (use existing packages):
  ```powershell
  .\start.ps1 -SkipBuild
  ```

- **Force restart** (clean slate):
  ```powershell
  .\start.ps1 -Force
  ```

- **Stop and remove all data**:
  ```powershell
  .\stop.ps1 -RemoveVolumes
  ```

- **Clean all Docker resources**:
  ```powershell
  .\stop.ps1 -CleanAll
  ```

## Docker Services

### LocalStack Pro
- **Image**: `localstack/localstack-pro:latest`
- **Ports**: 4566 (main), 45660 (compatibility), 443 (HTTPS)
- **Services**: Cognito, DynamoDB, Lambda, API Gateway, S3, IAM, CloudFormation
- **Persistence**: Enabled via volumes

### Lambda Builder
- **Image**: Custom (Node.js 18 Alpine)
- **Purpose**: Builds TypeScript Lambda functions
- **Profile**: `build`
- **Output**: ZIP packages in `./packages/`

### AWS Initializer
- **Image**: Custom (Alpine with AWS CLI)
- **Purpose**: Deploys infrastructure and initializes services
- **Profile**: `init`
- **Dependencies**: LocalStack (healthy), Lambda Builder (completed)

## File Structure

```
aws-backend/
├── docker/
│   ├── lambda-builder.Dockerfile      # Lambda build container
│   ├── deployer.Dockerfile           # AWS deployment container
│   ├── build-all-lambdas.sh         # Lambda build script
│   ├── deploy-infrastructure.sh      # Infrastructure deployment
│   └── init-gameflex.sh             # Complete initialization orchestrator
├── docker-compose.yml               # Docker services configuration
├── start.ps1                       # Main startup script
├── stop.ps1                        # Shutdown script
├── test-docker-setup.ps1           # Test suite
└── DOCKER_SETUP.md                 # This documentation
```

## Environment Variables

Key environment variables in `.env`:

```bash
# LocalStack Pro
LOCALSTACK_AUTH_TOKEN=ls-NIdUsIdA-lIza-5087-Fuji-jorOpuPAb667

# AWS Configuration
AWS_DEFAULT_REGION=us-east-1
AWS_ACCESS_KEY_ID=test
AWS_SECRET_ACCESS_KEY=test

# Docker Configuration
LOCALSTACK_DOCKER_NAME=gameflex-localstack
LOCALSTACK_VOLUME_DIR=./volume
```

## Initialization Process

The new setup follows this sequence:

1. **LocalStack Startup**: Container starts and becomes healthy
2. **Lambda Build**: TypeScript functions are compiled and packaged
3. **AWS Initialization**: 
   - Basic services (Cognito, DynamoDB, S3)
   - CloudFormation infrastructure deployment
   - Lambda function deployment
   - Service verification

## Benefits

### Compared to Previous Setup

- ✅ **Containerized**: All build and deployment logic in containers
- ✅ **Reproducible**: Consistent environment across machines
- ✅ **Faster**: Parallel builds and better caching
- ✅ **Cleaner**: No local Node.js/npm dependencies required
- ✅ **Isolated**: Build environment separate from host
- ✅ **Reliable**: Better error handling and dependency management

### LocalStack Best Practices

- ✅ **Init Hooks**: Uses LocalStack's recommended initialization patterns
- ✅ **Health Checks**: Proper service readiness detection
- ✅ **Volume Management**: Persistent data with proper cleanup options
- ✅ **Network Isolation**: Dedicated Docker network
- ✅ **Resource Cleanup**: Proper container lifecycle management

## Troubleshooting

### Common Issues

1. **LocalStack not starting**:
   ```powershell
   # Check auth token
   docker compose logs localstack
   
   # Force restart
   .\start.ps1 -Force
   ```

2. **Lambda build failures**:
   ```powershell
   # Test build only
   .\start.ps1 -BuildOnly
   
   # Check build logs
   docker compose --profile build logs lambda-builder
   ```

3. **Infrastructure deployment issues**:
   ```powershell
   # Check initialization logs
   docker compose --profile init logs aws-initializer
   
   # Manual AWS CLI test
   aws --endpoint-url=http://localhost:45660 sts get-caller-identity
   ```

### Debug Commands

```powershell
# Check service status
docker compose ps

# View logs
docker compose logs -f localstack

# Test LocalStack health
Invoke-WebRequest http://localhost:45660/_localstack/health

# List running containers
docker ps

# Clean everything
.\stop.ps1 -CleanAll
```

## Migration from Old Setup

The old setup used PowerShell scripts to build Lambda functions locally and deploy them. The new setup:

1. **Replaces** local npm/TypeScript builds with containerized builds
2. **Replaces** manual CloudFormation deployment with orchestrated containers
3. **Maintains** the same API endpoints and functionality
4. **Improves** reliability and reproducibility

### Breaking Changes

- Lambda functions must have proper `package.json` and TypeScript configuration
- Build output is now in `./packages/` instead of individual function directories
- Initialization is now atomic (all-or-nothing) instead of incremental

## Performance

### Build Times
- **Cold build**: ~2-3 minutes (includes image pulls)
- **Warm build**: ~30-60 seconds (cached images)
- **Lambda only**: ~15-30 seconds

### Resource Usage
- **Memory**: ~2GB (LocalStack Pro + builders)
- **Disk**: ~1GB (images + volumes)
- **CPU**: Moderate during build, low during runtime
