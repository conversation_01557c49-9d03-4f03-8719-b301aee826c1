/**
 * Global setup for Jest tests
 * Ensures LocalStack is running and services are available
 */

const { exec } = require('child_process');
const { promisify } = require('util');
const http = require('http');

const execAsync = promisify(exec);

function makeHttpRequest(url: string, method: string = 'GET', data?: string, headers?: Record<string, string>): Promise<string> {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname + urlObj.search,
      method: method,
      headers: headers || {}
    };

    const request = http.request(options, (response) => {
      let responseData = '';
      response.on('data', (chunk) => {
        responseData += chunk;
      });
      response.on('end', () => {
        if (response.statusCode && response.statusCode >= 200 && response.statusCode < 500) {
          resolve(responseData);
        } else {
          reject(new Error(`HTTP ${response.statusCode}: ${responseData}`));
        }
      });
    });

    request.on('error', (error) => {
      reject(error);
    });

    request.setTimeout(5000, () => {
      request.destroy();
      reject(new Error('Request timeout'));
    });

    if (data && (method === 'POST' || method === 'PUT')) {
      request.write(data);
    }

    request.end();
  });
}

async function checkLocalStackHealth() {
  try {
    const healthData = await makeHttpRequest('http://localhost:45660/_localstack/health');
    const health = JSON.parse(healthData);

    const requiredServices = ['lambda', 'apigateway', 'dynamodb', 's3', 'cognito-idp'];
    const availableServices = Object.keys(health.services || {});

    const allServicesRunning = requiredServices.every(service =>
      availableServices.includes(service) &&
      (health.services[service] === 'running' || health.services[service] === 'available')
    );

    if (allServicesRunning) {
      console.log('✅ LocalStack health check passed - all required services are running');
      return true;
    } else {
      console.log('❌ LocalStack health check failed - some services are not running');
      console.log('Required services:', requiredServices);
      console.log('Available services:', availableServices);
      console.log('Service statuses:', health.services);
      return false;
    }
  } catch (error) {
    console.log('❌ LocalStack health check failed - unable to connect');
    console.log('Error:', error.message);
    return false;
  }
}

async function waitForLocalStack(maxAttempts = 10, delayMs = 3000) {
  console.log('🔄 Waiting for LocalStack to be ready...');

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    const isHealthy = await checkLocalStackHealth();

    if (isHealthy) {
      console.log(`✅ LocalStack is ready after ${attempt} attempts`);
      return;
    }

    if (attempt < maxAttempts) {
      console.log(`⏳ Attempt ${attempt}/${maxAttempts} failed, retrying in ${delayMs}ms...`);
      await new Promise(resolve => setTimeout(resolve, delayMs));
    }
  }

  throw new Error(`❌ LocalStack failed to become ready after ${maxAttempts} attempts`);
}

async function checkBackendDeployment() {
  try {
    console.log('🔍 Checking if backend is deployed...');

    // Check if we can reach a simple endpoint
    const testUrl = 'http://localhost:45660/restapis';
    await makeHttpRequest(testUrl);

    console.log('✅ Backend appears to be accessible');
    return true;
  } catch (error) {
    console.log('⚠️  Backend not accessible, tests may need deployment');
    console.log('   Run: aws-backend/start.ps1 to deploy the backend');
    return false;
  }
}

async function setupApiGatewayUrl() {
  try {
    console.log('🔍 Discovering API Gateway configuration...');

    // First, try to get API Gateway URL from CloudFormation stack outputs
    try {
      const cfCommand = `aws --endpoint-url=http://localhost:45660 cloudformation describe-stacks --stack-name gameflex-infrastructure-development --query "Stacks[0].Outputs[?OutputKey=='ApiGatewayUrl'].OutputValue" --output text --region us-east-1`;
      const { stdout: cfOutput } = await execAsync(cfCommand);
      const apiGatewayUrl = cfOutput.trim();

      if (apiGatewayUrl && apiGatewayUrl !== 'None' && apiGatewayUrl !== '') {
        console.log(`📡 Found API Gateway URL from CloudFormation: ${apiGatewayUrl}`);

        // Test the API Gateway URL to make sure it's working
        const testEndpoint = `${apiGatewayUrl}/auth/signin`;
        console.log(`🔍 Testing API Gateway at: ${testEndpoint}`);

        try {
          const testResponse = await makeHttpRequest(testEndpoint, 'POST', '{}', { 'Content-Type': 'application/json' });
          console.log('✅ API Gateway is accessible and Lambda integration is working');
        } catch (testError) {
          // 400 error is expected for empty POST request
          if (testError.message.includes('400') || testError.message.includes('422')) {
            console.log('✅ API Gateway is accessible and Lambda integration is working (expected error)');
          } else {
            console.log('⚠️  API Gateway test failed:', testError.message);
          }
        }

        // Set the environment variable for other parts of the test suite
        process.env.API_GATEWAY_URL = apiGatewayUrl;
        console.log(`🔗 Using API Gateway URL: ${apiGatewayUrl}`);
        return;
      }
    } catch (cfError) {
      console.log('⚠️  Could not get API Gateway URL from CloudFormation, trying manual discovery...');
    }

    // Fallback: Use AWS CLI with LocalStack endpoint
    const awsCommand = `aws --endpoint-url=http://localhost:45660 apigateway get-rest-apis --region us-east-1`;
    const { stdout } = await execAsync(awsCommand);
    const apis = JSON.parse(stdout);

    if (!apis.items || apis.items.length === 0) {
      console.log('⚠️  No API Gateway REST APIs found - backend may not be deployed');
      console.log('   Run: aws-backend/start.ps1 to deploy the backend');
      return;
    }

    // Find the GameFlex API (or use the first one if only one exists)
    let targetApi = apis.items.find((api) =>
      api.name && api.name.toLowerCase().includes('gameflex')
    );

    if (!targetApi && apis.items.length === 1) {
      targetApi = apis.items[0];
    }

    if (!targetApi) {
      console.log('⚠️  GameFlex API Gateway not found');
      console.log('   Available APIs:', apis.items.map(api => api.name));
      return;
    }

    const apiId = targetApi.id;
    console.log(`📡 Found API Gateway: ${targetApi.name} (${apiId})`);

    // Get stages for this API
    const stagesCommand = `aws --endpoint-url=http://localhost:45660 apigateway get-stages --rest-api-id ${apiId} --region us-east-1`;
    const { stdout: stagesOutput } = await execAsync(stagesCommand);
    const stages = JSON.parse(stagesOutput);

    if (!stages.item || stages.item.length === 0) {
      console.log(`⚠️  No stages found for API ${apiId}`);
      return;
    }

    // Use development stage if available, otherwise use the first stage
    let targetStage = stages.item.find((stage) => stage.stageName === 'development');
    if (!targetStage) {
      targetStage = stages.item[0];
    }

    const stageName = targetStage.stageName;
    const apiGatewayUrl = `http://localhost:45660/restapis/${apiId}/${stageName}/_user_request_`;

    // Set the environment variable for other parts of the test suite
    process.env.API_GATEWAY_URL = apiGatewayUrl;

    console.log(`🔗 API Gateway URL: ${apiGatewayUrl}`);
  } catch (error) {
    console.log('⚠️  Failed to discover API Gateway URL:', error.message);
    console.log('   This is normal if the backend is not deployed yet');
    console.log('   Run: aws-backend/start.ps1 to deploy the backend');
  }
}

module.exports = async function globalSetup() {
  console.log('🚀 Starting GameFlex AWS Backend Test Suite');
  console.log('📋 Environment:', process.env.NODE_ENV || 'test');
  console.log('🔗 LocalStack URL:', process.env.AWS_ENDPOINT_URL || 'http://localhost:45660');

  try {
    // Set required environment variables
    process.env.AWS_ENDPOINT_URL = process.env.AWS_ENDPOINT_URL || 'http://localhost:45660';
    process.env.AWS_ACCESS_KEY_ID = process.env.AWS_ACCESS_KEY_ID || 'test';
    process.env.AWS_SECRET_ACCESS_KEY = process.env.AWS_SECRET_ACCESS_KEY || 'test';
    process.env.AWS_DEFAULT_REGION = process.env.AWS_DEFAULT_REGION || 'us-east-1';

    await waitForLocalStack();
    await checkBackendDeployment();
    await setupApiGatewayUrl();
    console.log('✅ Global setup completed successfully');
  } catch (error) {
    console.error('❌ Global setup failed:', error.message);
    console.log('');
    console.log('💡 Troubleshooting steps:');
    console.log('   1. Make sure LocalStack is running:');
    console.log('      cd aws-backend && ./start.ps1');
    console.log('   2. Wait for LocalStack to fully start (may take 1-2 minutes)');
    console.log('   3. Check Docker containers: docker ps');
    console.log('');
    throw error;
  }
}
