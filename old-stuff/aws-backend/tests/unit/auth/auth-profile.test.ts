/**
 * Unit tests for Auth Profile Management
 * Tests profile get and update functionality with JWT authentication
 */

import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { handler } from '../../../lambda-functions/auth/src/handler';
import { mockClient } from 'aws-sdk-client-mock';
import { DynamoDBDocumentClient, GetCommand, UpdateCommand, QueryCommand } from '@aws-sdk/lib-dynamodb';

// Mock AWS clients
const dynamoMock = mockClient(DynamoDBDocumentClient);

// Mock JWT decode function
const mockJwtDecode = jest.fn();

// Mock the jsonwebtoken module
jest.mock('jsonwebtoken', () => ({
  decode: mockJwtDecode
}));

// Mock context
const mockContext: Context = {
  callbackWaitsForEmptyEventLoop: false,
  functionName: 'test-function',
  functionVersion: '1',
  invokedFunctionArn: 'arn:aws:lambda:us-east-1:************:function:test-function',
  memoryLimitInMB: '128',
  awsRequestId: 'test-request-id',
  logGroupName: '/aws/lambda/test-function',
  logStreamName: '2023/01/01/[$LATEST]test-stream',
  getRemainingTimeInMillis: () => 30000,
  done: () => { },
  fail: () => { },
  succeed: () => { }
};

// Helper function to create API Gateway event
function createEvent(
  httpMethod: string,
  path: string,
  body?: any,
  headers?: Record<string, string>
): APIGatewayProxyEvent {
  return {
    httpMethod,
    path,
    body: body ? JSON.stringify(body) : null,
    headers: headers || {},
    multiValueHeaders: {},
    queryStringParameters: null,
    multiValueQueryStringParameters: null,
    pathParameters: null,
    stageVariables: null,
    requestContext: {
      accountId: '************',
      apiId: 'test-api',
      protocol: 'HTTP/1.1',
      httpMethod,
      path,
      stage: 'test',
      requestId: 'test-request',
      requestTime: '01/Jan/2023:00:00:00 +0000',
      requestTimeEpoch: *************,
      resourceId: 'test-resource',
      resourcePath: path,
      identity: {
        accessKey: null,
        accountId: null,
        apiKey: null,
        apiKeyId: null,
        caller: null,
        cognitoAuthenticationProvider: null,
        cognitoAuthenticationType: null,
        cognitoIdentityId: null,
        cognitoIdentityPoolId: null,
        principalOrgId: null,
        sourceIp: '127.0.0.1',
        user: null,
        userAgent: 'test-agent',
        userArn: null,
        clientCert: null
      },
      authorizer: null
    },
    resource: path,
    isBase64Encoded: false
  };
}

describe('Auth Profile Management', () => {
  const mockUser = {
    id: 'user-123',
    cognito_user_id: 'cognito-user-id-123',
    email: '<EMAIL>',
    username: 'testuser',
    display_name: 'Test User',
    avatar_url: 'https://example.com/avatar.jpg',
    is_verified: true,
    created_at: '2023-01-01T00:00:00.000Z',
    updated_at: '2023-01-01T00:00:00.000Z',
    last_login: '2023-01-01T00:00:00.000Z'
  };

  const mockTokenPayload = {
    sub: 'cognito-user-id-123',
    email: '<EMAIL>',
    username: 'testuser',
    aud: 'test-client-id',
    iss: 'https://cognito-idp.us-east-1.amazonaws.com/us-east-1_TestPool',
    exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour from now
    iat: Math.floor(Date.now() / 1000),
    token_use: 'access'
  };

  beforeEach(() => {
    // Reset all mocks
    dynamoMock.reset();
    mockJwtDecode.mockReset();

    // Set environment variables
    process.env.DYNAMODB_TABLE_USERS = 'test-users-table';
  });

  describe('GET /auth/profile', () => {
    it('should get user profile successfully with real auth flow', async () => {
      // First create a new user
      const uniqueEmail = `profile-test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}@example.com`;
      const uniqueUsername = `profileuser${Date.now()}`;

      const signupEvent = createEvent('POST', '/auth/signup', {
        email: uniqueEmail,
        password: 'TestPassword123!',
        username: uniqueUsername
      });

      const signupResult = await handler(signupEvent, mockContext);
      expect(signupResult.statusCode).toBe(201);

      // Then sign in to get real tokens
      const signinEvent = createEvent('POST', '/auth/signin', {
        email: uniqueEmail,
        password: 'TestPassword123!'
      });

      const signinResult = await handler(signinEvent, mockContext);
      expect(signinResult.statusCode).toBe(200);

      const signinBody = JSON.parse(signinResult.body);
      const accessToken = signinBody.tokens.access_token;

      // Now test profile access with real token
      const profileEvent = createEvent('GET', '/auth/profile', undefined, {
        Authorization: `Bearer ${accessToken}`
      });

      const result = await handler(profileEvent, mockContext);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.user.email).toBe(uniqueEmail);
      expect(body.user.username).toBe(uniqueUsername);
      expect(body.user.id).toBeDefined();
    });

    it('should return error for missing authorization header', async () => {
      const event = createEvent('GET', '/auth/profile');

      const result = await handler(event, mockContext);

      expect(result.statusCode).toBe(401);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Authorization header required');
    });

    it('should return error for invalid JWT token', async () => {
      mockJwtDecode.mockReturnValue(null);

      const event = createEvent('GET', '/auth/profile', undefined, {
        Authorization: 'Bearer invalid-token'
      });

      const result = await handler(event, mockContext);

      expect(result.statusCode).toBe(401);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Invalid or expired token');
    });

    it('should return error for expired JWT token', async () => {
      const expiredPayload = {
        ...mockTokenPayload,
        exp: Math.floor(Date.now() / 1000) - 3600 // 1 hour ago
      };
      mockJwtDecode.mockReturnValue(expiredPayload);

      const event = createEvent('GET', '/auth/profile', undefined, {
        Authorization: 'Bearer expired-token'
      });

      const result = await handler(event, mockContext);

      expect(result.statusCode).toBe(401);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Invalid or expired token');
    });

    it('should return error when user not found in database', async () => {
      mockJwtDecode.mockReturnValue(mockTokenPayload);
      dynamoMock.on(QueryCommand).resolves({ Items: [] }); // No user found

      const event = createEvent('GET', '/auth/profile', undefined, {
        Authorization: 'Bearer valid-jwt-token'
      });

      const result = await handler(event, mockContext);

      expect(result.statusCode).toBe(401);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Invalid or expired token');
    });

    // Note: Profile not found scenario doesn't occur in real usage since profiles are created during signup
  });

  describe('PUT /auth/profile', () => {
    it('should update user profile successfully with real auth flow', async () => {
      // First create a new user
      const uniqueEmail = `update-test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}@example.com`;
      const uniqueUsername = `updateuser${Date.now()}`;

      const signupEvent = createEvent('POST', '/auth/signup', {
        email: uniqueEmail,
        password: 'TestPassword123!',
        username: uniqueUsername
      });

      const signupResult = await handler(signupEvent, mockContext);
      expect(signupResult.statusCode).toBe(201);

      // Then sign in to get real tokens
      const signinEvent = createEvent('POST', '/auth/signin', {
        email: uniqueEmail,
        password: 'TestPassword123!'
      });

      const signinResult = await handler(signinEvent, mockContext);
      expect(signinResult.statusCode).toBe(200);

      const signinBody = JSON.parse(signinResult.body);
      const accessToken = signinBody.tokens.access_token;

      // Now test profile update with real token
      const updateEvent = createEvent('PUT', '/auth/profile', {
        display_name: 'Updated Name',
        avatar_url: 'https://example.com/new-avatar.jpg'
      }, {
        Authorization: `Bearer ${accessToken}`
      });

      const result = await handler(updateEvent, mockContext);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.message).toBe('Profile updated successfully');
      expect(body.user.display_name).toBe('Updated Name');
      expect(body.user.avatar_url).toBe('https://example.com/new-avatar.jpg');
    });

    it('should return error for no valid fields to update with real auth flow', async () => {
      // First create a new user
      const uniqueEmail = `nofields-test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}@example.com`;
      const uniqueUsername = `nofieldsuser${Date.now()}`;

      const signupEvent = createEvent('POST', '/auth/signup', {
        email: uniqueEmail,
        password: 'TestPassword123!',
        username: uniqueUsername
      });

      const signupResult = await handler(signupEvent, mockContext);
      expect(signupResult.statusCode).toBe(201);

      // Then sign in to get real tokens
      const signinEvent = createEvent('POST', '/auth/signin', {
        email: uniqueEmail,
        password: 'TestPassword123!'
      });

      const signinResult = await handler(signinEvent, mockContext);
      expect(signinResult.statusCode).toBe(200);

      const signinBody = JSON.parse(signinResult.body);
      const accessToken = signinBody.tokens.access_token;

      // Now test profile update with invalid fields
      const updateEvent = createEvent('PUT', '/auth/profile', {
        invalid_field: 'some value'
      }, {
        Authorization: `Bearer ${accessToken}`
      });

      const result = await handler(updateEvent, mockContext);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('No valid fields to update');
    });

    it('should only update allowed fields with real auth flow', async () => {
      // First create a new user
      const uniqueEmail = `allowed-test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}@example.com`;
      const uniqueUsername = `alloweduser${Date.now()}`;

      const signupEvent = createEvent('POST', '/auth/signup', {
        email: uniqueEmail,
        password: 'TestPassword123!',
        username: uniqueUsername
      });

      const signupResult = await handler(signupEvent, mockContext);
      expect(signupResult.statusCode).toBe(201);

      // Then sign in to get real tokens
      const signinEvent = createEvent('POST', '/auth/signin', {
        email: uniqueEmail,
        password: 'TestPassword123!'
      });

      const signinResult = await handler(signinEvent, mockContext);
      expect(signinResult.statusCode).toBe(200);

      const signinBody = JSON.parse(signinResult.body);
      const accessToken = signinBody.tokens.access_token;

      // Now test profile update with real token and restricted fields
      const updateEvent = createEvent('PUT', '/auth/profile', {
        display_name: 'Updated Name',
        email: '<EMAIL>', // This should be ignored
        is_verified: false // This should be ignored
      }, {
        Authorization: `Bearer ${accessToken}`
      });

      const result = await handler(updateEvent, mockContext);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.user.display_name).toBe('Updated Name');
      expect(body.user.email).toBe(uniqueEmail); // Should remain unchanged
    });

    it('should return error for missing authorization', async () => {
      const event = createEvent('PUT', '/auth/profile', {
        display_name: 'Updated Name'
      });

      const result = await handler(event, mockContext);

      expect(result.statusCode).toBe(401);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Authorization header required');
    });
  });

  describe('Endpoint not found', () => {
    it('should return 404 for unknown endpoints', async () => {
      const event = createEvent('GET', '/auth/unknown-endpoint');

      const result = await handler(event, mockContext);

      expect(result.statusCode).toBe(404);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Endpoint not found');
    });
  });
});
