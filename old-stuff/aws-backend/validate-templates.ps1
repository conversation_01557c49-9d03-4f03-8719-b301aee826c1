# GameFlex CloudFormation Template Validation Script
# This script validates all CloudFormation templates using cfn-lint and AWS CLI

param(
    [switch]$InstallCfnLint,
    [switch]$Verbose,
    [string]$Template = ""
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Header {
    param([string]$Message)
    Write-Host "[VALIDATE] $Message" -ForegroundColor Blue
}

# Install cfn-lint if requested
function Install-CfnLint {
    Write-Header "Installing cfn-lint"
    
    try {
        # Check if Python is installed
        python --version 2>$null
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Python is required to install cfn-lint. Please install Python first."
            return $false
        }
        
        # Install cfn-lint
        Write-Status "Installing cfn-lint via pip..."
        pip install cfn-lint
        
        if ($LASTEXITCODE -eq 0) {
            Write-Status "cfn-lint installed successfully"
            return $true
        }
        else {
            Write-Error "Failed to install cfn-lint"
            return $false
        }
    }
    catch {
        Write-Error "Failed to install cfn-lint: $_"
        return $false
    }
}

# Check if cfn-lint is available
function Test-CfnLint {
    try {
        cfn-lint --version 2>$null | Out-Null
        if ($LASTEXITCODE -eq 0) {
            $version = cfn-lint --version
            Write-Status "cfn-lint found: $version"
            return $true
        }
        else {
            Write-Warning "cfn-lint not found. Use -InstallCfnLint to install it."
            return $false
        }
    }
    catch {
        Write-Warning "cfn-lint not available: $_"
        return $false
    }
}

# Validate template with AWS CLI
function Test-AwsTemplate {
    param([string]$TemplateFile)
    
    Write-Status "Validating with AWS CLI: $TemplateFile"
    
    try {
        aws cloudformation validate-template --template-body file://$TemplateFile 2>$null | Out-Null
        
        if ($LASTEXITCODE -eq 0) {
            Write-Status "AWS CLI validation passed: $TemplateFile"
            return $true
        }
        else {
            Write-Error "AWS CLI validation failed: $TemplateFile"
            return $false
        }
    }
    catch {
        Write-Error "AWS CLI validation error: $TemplateFile - $($_.Exception.Message)"
        return $false
    }
}

# Validate template with cfn-lint
function Test-CfnLintTemplate {
    param([string]$TemplateFile)
    
    Write-Status "Validating with cfn-lint: $TemplateFile"
    
    try {
        $output = cfn-lint $TemplateFile 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Status "cfn-lint validation passed: $TemplateFile"
            return $true
        }
        else {
            Write-Error "cfn-lint validation failed: $TemplateFile"
            Write-Host $output -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Error "cfn-lint validation error: $TemplateFile - $($_.Exception.Message)"
        return $false
    }
}

# Check for security best practices
function Test-SecurityBestPractices {
    param([string]$TemplateFile)
    
    Write-Status "Checking security best practices: $TemplateFile"
    
    $content = Get-Content $TemplateFile -Raw
    $issues = @()
    
    # Check for hardcoded passwords (only flag for non-LocalStack templates)
    if ($TemplateFile -notlike "*simple*" -and $TemplateFile -notlike "*localstack*") {
        # Only flag if password is directly hardcoded, not if using Secrets Manager
        if ($content -match 'password.*:.*"[^{].*"' -or $content -match 'Password.*:.*"[^{].*"') {
            if ($content -notmatch 'secretsmanager' -and $content -notmatch 'resolve:') {
                $issues += "Potential hardcoded password found"
            }
        }

        if ($content -match 'MasterUserPassword.*:.*"[^{].*"') {
            if ($content -notmatch 'secretsmanager' -and $content -notmatch 'resolve:') {
                $issues += "Hardcoded database password found"
            }
        }
    }
    
    # Check for public S3 buckets
    if ($content -match 'PublicRead' -or $content -match 'PublicReadWrite') {
        $issues += "Public S3 bucket access detected"
    }
    
    # Check for deprecated properties
    if ($content -match 'AccessControl:') {
        $issues += "Deprecated S3 AccessControl property found (use OwnershipControls instead)"
    }
    
    # Check for missing encryption
    if ($content -match 'AWS::RDS::DBInstance' -and $content -notmatch 'StorageEncrypted') {
        $issues += "RDS instance without encryption configuration"
    }
    
    # Check for missing backup configuration
    if ($content -match 'AWS::RDS::DBInstance' -and $content -notmatch 'BackupRetentionPeriod') {
        $issues += "RDS instance without backup configuration"
    }
    
    if ($issues.Count -eq 0) {
        Write-Status "Security best practices check passed: $TemplateFile"
        return $true
    }
    else {
        Write-Warning "Security issues found in: $TemplateFile"
        foreach ($issue in $issues) {
            Write-Warning "  - $issue"
        }
        return $false
    }
}

# Main validation function
function Invoke-TemplateValidation {
    Write-Header "GameFlex CloudFormation Template Validation"
    Write-Host ""
    
    # Get list of templates to validate
    $templates = if ($Template) {
        @($Template)
    }
    else {
        Get-ChildItem -Path "cloudformation" -Filter "*.yaml" | ForEach-Object { $_.FullName }
    }
    
    if ($templates.Count -eq 0) {
        Write-Error "No CloudFormation templates found"
        return $false
    }
    
    Write-Status "Found $($templates.Count) template(s) to validate"
    Write-Host ""
    
    $allPassed = $true
    $cfnLintAvailable = Test-CfnLint
    
    foreach ($templateFile in $templates) {
        $templateName = Split-Path $templateFile -Leaf
        Write-Header "Validating: $templateName"
        
        # Check if file exists
        if (-not (Test-Path $templateFile)) {
            Write-Error "Template file not found: $templateFile"
            $allPassed = $false
            continue
        }
        
        # AWS CLI validation
        $awsValid = Test-AwsTemplate -TemplateFile $templateFile
        if (-not $awsValid) {
            $allPassed = $false
        }
        
        # cfn-lint validation (if available)
        if ($cfnLintAvailable) {
            $cfnLintValid = Test-CfnLintTemplate -TemplateFile $templateFile
            if (-not $cfnLintValid) {
                $allPassed = $false
            }
        }
        
        # Security best practices check
        $securityValid = Test-SecurityBestPractices -TemplateFile $templateFile
        if (-not $securityValid) {
            # Don't fail overall validation for security warnings, just warn
            Write-Warning "Security recommendations available for: $templateName"
        }
        
        Write-Host ""
    }
    
    # Summary
    Write-Header "Validation Summary"
    if ($allPassed) {
        Write-Status "All templates passed validation!"
    }
    else {
        Write-Error "Some templates failed validation"
    }
    
    if (-not $cfnLintAvailable) {
        Write-Warning "Install cfn-lint for more comprehensive validation: pip install cfn-lint"
    }
    
    return $allPassed
}

# Show help if requested
if ($args -contains "-h" -or $args -contains "--help") {
    Write-Host "GameFlex CloudFormation Template Validation Script" -ForegroundColor Blue
    Write-Host ""
    Write-Host "Usage: .\validate-templates.ps1 [OPTIONS]" -ForegroundColor Green
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Yellow
    Write-Host "  -Template       Validate specific template file"
    Write-Host "  -InstallCfnLint Install cfn-lint via pip"
    Write-Host "  -Verbose        Show verbose output"
    Write-Host "  -h, -help       Show this help message"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\validate-templates.ps1"
    Write-Host "  .\validate-templates.ps1 -Template cloudformation\gameflex-production-infrastructure.yaml"
    Write-Host "  .\validate-templates.ps1 -InstallCfnLint"
    exit 0
}

# Install cfn-lint if requested
if ($InstallCfnLint) {
    if (Install-CfnLint) {
        Write-Status "cfn-lint installation completed"
    }
    else {
        Write-Error "cfn-lint installation failed"
        exit 1
    }
}

# Run validation
try {
    $result = Invoke-TemplateValidation
    if (-not $result) {
        exit 1
    }
}
catch {
    Write-Error "Validation failed: $($_.Exception.Message)"
    exit 1
}
