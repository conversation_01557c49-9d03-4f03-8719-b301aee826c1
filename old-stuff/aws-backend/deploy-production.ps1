# GameFlex Production Environment Deployment Script (PowerShell)
# This script deploys the AWS infrastructure to the Production environment using real AWS services

param(
    [string]$ProjectName = "gameflex",
    [string]$Region = "us-east-1",
    [string]$Profile = "production",
    [switch]$Force,
    [switch]$Verbose,
    [switch]$DryRun
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Environment configuration
$Environment = "production"
$StackName = "$ProjectName-infrastructure-$Environment"
$TemplateFile = "cloudformation\gameflex-production-infrastructure.yaml"
$ParametersFile = "cloudformation\parameters\production.json"

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Header {
    param([string]$Message)
    Write-Host "[DEPLOY-PROD] $Message" -ForegroundColor Magenta
}

# Validate prerequisites
function Test-Prerequisites {
    Write-Status "Validating prerequisites..."
    
    # Check AWS CLI
    try {
        $awsVersion = aws --version 2>$null
        if ($LASTEXITCODE -ne 0) {
            throw "AWS CLI not found"
        }
        Write-Status "AWS CLI found: $awsVersion"
    }
    catch {
        Write-Error "AWS CLI is required but not installed. Please install AWS CLI v2."
        return $false
    }
    
    # Check AWS credentials
    try {
        $identity = aws sts get-caller-identity --profile $Profile --region $Region 2>$null | ConvertFrom-Json
        if ($LASTEXITCODE -ne 0) {
            throw "Failed to get AWS identity"
        }
        Write-Status "AWS Identity: $($identity.Arn)"
        Write-Status "Account ID: $($identity.Account)"
        
        # Additional validation for production
        if ($identity.Account -eq "************") {
            Write-Error "You appear to be using a test/example AWS account. Please use a real production AWS account."
            return $false
        }
    }
    catch {
        Write-Error "Failed to validate AWS credentials. Please configure AWS CLI with 'aws configure --profile $Profile'"
        return $false
    }
    
    # Check required files
    if (-not (Test-Path $TemplateFile)) {
        Write-Error "CloudFormation template not found: $TemplateFile"
        return $false
    }
    
    if (-not (Test-Path $ParametersFile)) {
        Write-Error "Parameters file not found: $ParametersFile"
        return $false
    }
    
    # Validate parameters file for production-specific values
    try {
        $parameters = Get-Content $ParametersFile | ConvertFrom-Json
        $certParam = $parameters | Where-Object { $_.ParameterKey -eq "CertificateArn" }
        if ($certParam -and $certParam.ParameterValue -like "*ACCOUNT_ID*") {
            Write-Error "Production parameters file contains placeholder values. Please update $ParametersFile with real values."
            return $false
        }
    }
    catch {
        Write-Error "Failed to validate parameters file: $_"
        return $false
    }
    
    Write-Status "Prerequisites validation completed successfully"
    return $true
}

# Validate CloudFormation template
function Test-CloudFormationTemplate {
    Write-Status "Validating CloudFormation template..."
    
    try {
        aws cloudformation validate-template --template-body file://$TemplateFile --profile $Profile --region $Region | Out-Null
        if ($LASTEXITCODE -ne 0) {
            throw "Template validation failed"
        }
        Write-Status "CloudFormation template is valid"
        return $true
    }
    catch {
        Write-Error "CloudFormation template validation failed: $_"
        return $false
    }
}

# Package Lambda functions
function New-LambdaPackage {
    param([string]$FunctionPath, [string]$OutputPath)

    Write-Status "Packaging Lambda function: $FunctionPath"

    try {
        # Check if this is a TypeScript function
        $isTypeScript = Test-Path "$FunctionPath\package.json"

        if ($isTypeScript) {
            Write-Status "Building TypeScript Lambda function..."

            # Install dependencies and build
            Push-Location $FunctionPath
            try {
                npm install --production=false
                npm run build
                npm ci --production

                # Create ZIP package
                $zipPath = "$OutputPath\$(Split-Path $FunctionPath -Leaf).zip"
                if (Test-Path $zipPath) {
                    Remove-Item $zipPath -Force
                }

                # Package dist folder and node_modules
                $tempDir = New-TemporaryFile | ForEach-Object { Remove-Item $_; New-Item -ItemType Directory -Path $_ }

                if (Test-Path "dist") {
                    Copy-Item -Path "dist\*" -Destination $tempDir -Recurse
                }
                if (Test-Path "node_modules") {
                    Copy-Item -Path "node_modules" -Destination $tempDir -Recurse
                }
                if (Test-Path "package.json") {
                    Copy-Item -Path "package.json" -Destination $tempDir
                }

                # Check if we have files to compress
                if ((Get-ChildItem $tempDir).Count -gt 0) {
                    Compress-Archive -Path "$tempDir\*" -DestinationPath $zipPath -Force
                }
                else {
                    # Create empty zip if no files
                    Add-Type -AssemblyName System.IO.Compression.FileSystem
                    [System.IO.Compression.ZipFile]::CreateFromDirectory($tempDir, $zipPath)
                }

                Remove-Item $tempDir -Recurse -Force
            }
            finally {
                Pop-Location
            }
        }

        Write-Status "Lambda package created: $zipPath"
        return $zipPath
    }
    catch {
        Write-Error "Failed to package Lambda function: $_"
        return $null
    }
}

# Deploy CloudFormation stack
function Deploy-CloudFormationStack {
    Write-Status "Deploying CloudFormation stack: $StackName"
    
    try {
        # Check if stack exists
        $stackExists = $false
        try {
            aws cloudformation describe-stacks --stack-name $StackName --profile $Profile --region $Region 2>$null | Out-Null
            if ($LASTEXITCODE -eq 0) {
                $stackExists = $true
                Write-Status "Stack $StackName already exists, updating..."
            }
        }
        catch {
            Write-Status "Stack $StackName does not exist, creating..."
        }
        
        # Prepare deployment command
        $deployCommand = if ($stackExists) {
            "aws cloudformation update-stack"
        } else {
            "aws cloudformation create-stack"
        }
        
        $deployCommand += " --stack-name $StackName"
        $deployCommand += " --template-body file://$TemplateFile"
        $deployCommand += " --parameters file://$ParametersFile"
        $deployCommand += " --capabilities CAPABILITY_NAMED_IAM CAPABILITY_IAM"
        $deployCommand += " --profile $Profile"
        $deployCommand += " --region $Region"
        $deployCommand += " --tags Key=Environment,Value=$Environment Key=Project,Value=$ProjectName"
        
        # Add termination protection for production
        if (-not $stackExists) {
            $deployCommand += " --enable-termination-protection"
        }
        
        if ($DryRun) {
            Write-Status "DRY RUN - Would execute: $deployCommand"
            return $true
        }
        
        # Execute deployment
        Invoke-Expression $deployCommand | Out-Null
        
        if ($LASTEXITCODE -ne 0) {
            throw "CloudFormation deployment failed"
        }
        
        # Wait for stack to complete
        Write-Status "Waiting for stack deployment to complete..."
        $timeout = 3600  # 60 minutes for production
        $counter = 0
        
        do {
            Start-Sleep -Seconds 30
            $counter += 30
            
            $stackStatus = aws cloudformation describe-stacks --stack-name $StackName --profile $Profile --region $Region --query "Stacks[0].StackStatus" --output text 2>$null
            
            Write-Status "Stack status: $stackStatus"
            
            if ($stackStatus -match "COMPLETE") {
                Write-Status "Stack deployment completed with status: $stackStatus"
                return $true
            }
            elseif ($stackStatus -match "FAILED" -or $stackStatus -match "ROLLBACK") {
                Write-Error "Stack deployment failed with status: $stackStatus"
                
                # Get stack events for debugging
                Write-Status "Recent stack events:"
                aws cloudformation describe-stack-events --stack-name $StackName --profile $Profile --region $Region --query "StackEvents[?ResourceStatus=='CREATE_FAILED' || ResourceStatus=='UPDATE_FAILED'].{Resource:LogicalResourceId,Status:ResourceStatus,Reason:ResourceStatusReason}" --output table
                
                return $false
            }
            
            if ($counter -ge $timeout) {
                Write-Error "Stack deployment timeout"
                return $false
            }
            
        } while ($true)
    }
    catch {
        Write-Error "Failed to deploy CloudFormation stack: $_"
        return $false
    }
}

# Get stack outputs
function Get-StackOutputs {
    param([string]$StackName)
    
    try {
        $outputs = aws cloudformation describe-stacks --stack-name $StackName --profile $Profile --region $Region --query "Stacks[0].Outputs" --output json | ConvertFrom-Json
        
        $outputHash = @{}
        foreach ($output in $outputs) {
            $outputHash[$output.OutputKey] = $output.OutputValue
        }
        
        return $outputHash
    }
    catch {
        Write-Warning "Failed to get stack outputs: $_"
        return @{}
    }
}

# Main deployment function
function Deploy-ProductionInfrastructure {
    Write-Header "Deploying GameFlex Production Infrastructure"
    Write-Host ""
    Write-Warning "⚠️  PRODUCTION DEPLOYMENT ⚠️"
    Write-Host ""
    Write-Status "Environment: $Environment"
    Write-Status "Region: $Region"
    Write-Status "Profile: $Profile"
    Write-Status "Stack Name: $StackName"
    Write-Host ""
    
    # Validate prerequisites
    if (-not (Test-Prerequisites)) {
        Write-Error "Prerequisites validation failed"
        exit 1
    }
    
    # Validate template
    if (-not (Test-CloudFormationTemplate)) {
        Write-Error "Template validation failed"
        exit 1
    }
    
    # Multiple confirmation prompts for production deployment
    if (-not $Force -and -not $DryRun) {
        Write-Warning "🚨 CRITICAL: You are about to deploy to the PRODUCTION environment! 🚨"
        Write-Warning "This will create real AWS resources that will incur costs and serve live traffic."
        Write-Warning "Make sure you have:"
        Write-Warning "  1. Tested this deployment in QA environment"
        Write-Warning "  2. Updated all parameter values in $ParametersFile"
        Write-Warning "  3. Verified SSL certificates and domain names"
        Write-Warning "  4. Coordinated with your team"
        Write-Host ""
        
        $confirmation1 = Read-Host "Type 'PRODUCTION' to confirm you want to deploy to production"
        if ($confirmation1 -ne "PRODUCTION") {
            Write-Status "Deployment cancelled - incorrect confirmation"
            exit 0
        }
        
        $confirmation2 = Read-Host "Are you absolutely sure? This cannot be easily undone. (yes/no)"
        if ($confirmation2 -ne "yes") {
            Write-Status "Deployment cancelled by user"
            exit 0
        }
    }
    
    # Create packages directory
    $packagesDir = "packages"
    if (-not (Test-Path $packagesDir)) {
        New-Item -ItemType Directory -Path $packagesDir | Out-Null
    }
    
    # Package Lambda functions
    Write-Status "Packaging Lambda functions..."
    $authPackage = New-LambdaPackage -FunctionPath "lambda-functions\auth" -OutputPath $packagesDir
    $postsPackage = New-LambdaPackage -FunctionPath "lambda-functions\posts" -OutputPath $packagesDir
    $mediaPackage = New-LambdaPackage -FunctionPath "lambda-functions\media" -OutputPath $packagesDir

    if (-not $authPackage -or -not $postsPackage -or -not $mediaPackage) {
        Write-Error "Failed to package Lambda functions"
        exit 1
    }
    
    # Deploy CloudFormation stack
    $deployResult = Deploy-CloudFormationStack
    
    if (-not $deployResult) {
        Write-Error "Failed to deploy CloudFormation stack"
        exit 1
    }
    
    if ($DryRun) {
        Write-Status "DRY RUN completed successfully"
        exit 0
    }
    
    # Get stack outputs
    $outputs = Get-StackOutputs -StackName $StackName
    
    # Display deployment information
    Write-Host ""
    Write-Header "🎉 Production Deployment completed successfully! 🎉"
    Write-Host ""
    
    Write-Status "Stack Outputs:"
    foreach ($key in $outputs.Keys) {
        Write-Host "  $key : $($outputs[$key])" -ForegroundColor Cyan
    }
    
    Write-Host ""
    Write-Status "Critical Next Steps:"
    Write-Host "  1. 🔍 Verify all services are healthy" -ForegroundColor Yellow
    Write-Host "  2. 🌐 Update DNS records for custom domains" -ForegroundColor Yellow
    Write-Host "  3. 🧪 Run smoke tests against production endpoints" -ForegroundColor Yellow
    Write-Host "  4. 📊 Set up monitoring and alerting" -ForegroundColor Yellow
    Write-Host "  5. 🔐 Verify security configurations" -ForegroundColor Yellow
    Write-Host "  6. 📝 Update documentation with production URLs" -ForegroundColor Yellow
    Write-Host "  7. 👥 Notify team of successful deployment" -ForegroundColor Yellow
}

# Show help if requested
if ($args -contains "-h" -or $args -contains "--help") {
    Write-Host "GameFlex Production Infrastructure Deployment Script" -ForegroundColor Blue
    Write-Host ""
    Write-Host "Usage: .\deploy-production.ps1 [OPTIONS]" -ForegroundColor Green
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Yellow
    Write-Host "  -ProjectName    Project name (default: gameflex)"
    Write-Host "  -Region         AWS region (default: us-east-1)"
    Write-Host "  -Profile        AWS profile (default: production)"
    Write-Host "  -Force          Skip confirmation prompts (NOT RECOMMENDED)"
    Write-Host "  -DryRun         Show what would be deployed without actually deploying"
    Write-Host "  -Verbose        Show verbose output"
    Write-Host "  -h, --help      Show this help message"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\deploy-production.ps1 -DryRun"
    Write-Host "  .\deploy-production.ps1 -Profile prod-profile"
    Write-Host "  .\deploy-production.ps1 -Region us-west-2"
    exit 0
}

# Run deployment
try {
    Deploy-ProductionInfrastructure
}
catch {
    Write-Error "Production deployment failed: $_"
    exit 1
}
