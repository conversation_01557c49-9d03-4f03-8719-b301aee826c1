#!/bin/bash

# GameFlex Media Upload Script
# This script uploads media files to S3 for seeded posts
# Runs automatically after DynamoDB seeding

set -e

echo "[S3] Uploading media files to S3..."

# S3 bucket name
MEDIA_BUCKET="gameflex-media-development"

# Function to upload file to S3
upload_media_file() {
    local file_path=$1
    local s3_key=$2
    local content_type=$3

    if [ ! -f "$file_path" ]; then
        echo "[ERROR] Media file not found: $file_path"
        return 1
    fi

    echo "[INFO] Uploading $file_path to s3://$MEDIA_BUCKET/$s3_key"
    
    awslocal s3 cp "$file_path" "s3://$MEDIA_BUCKET/$s3_key" \
        --content-type "$content_type" \
        --metadata-directive REPLACE

    if [ $? -eq 0 ]; then
        echo "[INFO] Successfully uploaded $s3_key"
        
        # Verify the upload
        awslocal s3 ls "s3://$MEDIA_BUCKET/$s3_key" > /dev/null
        if [ $? -eq 0 ]; then
            echo "[INFO] Verified $s3_key exists in S3"
        else
            echo "[WARN] Could not verify $s3_key in S3"
        fi
    else
        echo "[ERROR] Failed to upload $s3_key"
        return 1
    fi
}

# Check if media bucket exists, create if it doesn't
echo "[INFO] Checking if S3 bucket exists: $MEDIA_BUCKET"
awslocal s3 ls "s3://$MEDIA_BUCKET" > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "[INFO] S3 bucket $MEDIA_BUCKET does not exist, creating it..."
    awslocal s3 mb "s3://$MEDIA_BUCKET"

    # Set bucket policy to allow public read access
    awslocal s3api put-bucket-policy --bucket "$MEDIA_BUCKET" --policy '{
        "Version": "2012-10-17",
        "Statement": [
            {
                "Sid": "PublicReadGetObject",
                "Effect": "Allow",
                "Principal": "*",
                "Action": "s3:GetObject",
                "Resource": "arn:aws:s3:::'$MEDIA_BUCKET'/*"
            }
        ]
    }'

    echo "[INFO] Created S3 bucket $MEDIA_BUCKET with public read policy"
else
    echo "[INFO] S3 bucket $MEDIA_BUCKET already exists"
fi

# Upload media files for seeded posts
echo "[INFO] Uploading media files for seeded posts..."

# Upload COD screenshot for John Doe's post
upload_media_file \
    "/opt/code/assets/media/cod_screenshot.jpg" \
    "user/00000000-0000-0000-0000-000000000003/cod_screenshot.jpg" \
    "image/jpeg"

# Upload Diablo screenshot for Jane Smith's post
upload_media_file \
    "/opt/code/assets/media/diablo_screenshot.webp" \
    "user/00000000-0000-0000-0000-000000000004/diablo_screenshot.webp" \
    "image/webp"

# List uploaded files
echo "[INFO] Media files in S3 bucket:"
awslocal s3 ls "s3://$MEDIA_BUCKET/" --recursive

echo "[S3] Media upload completed successfully!"
echo "[INFO] Uploaded 2 media files for seeded posts"
