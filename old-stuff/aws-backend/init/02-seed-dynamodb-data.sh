#!/bin/bash

# GameFlex DynamoDB Data Seeding Script
# This script seeds DynamoDB tables with initial development data
# Runs automatically after table creation

set -e

echo "[DYNAMODB] Seeding DynamoDB tables with development data..."

# Function to put item in DynamoDB using awslocal
put_item() {
    local table_name=$1
    local item_json=$2

    echo "$item_json" | awslocal dynamodb put-item --table-name "$table_name" --item file:///dev/stdin

    if [ $? -eq 0 ]; then
        echo "[INFO] Added item to $table_name"
    else
        echo "[ERROR] Failed to add item to $table_name"
        return 1
    fi
}

# Function to get Cognito user ID by email
get_cognito_user_id() {
    local email=$1
    local user_pool_id=$2

    local cognito_user_id=$(awslocal cognito-idp list-users \
        --user-pool-id "$user_pool_id" \
        --filter "email = \"$email\"" \
        --query 'Users[0].Username' \
        --output text 2>/dev/null || echo "None")

    if [ "$cognito_user_id" != "None" ] && [ "$cognito_user_id" != "" ]; then
        echo "$cognito_user_id"
    else
        echo "cognito-user-not-found-$email"
    fi
}

# Seed Users table
seed_users() {
    echo "[INFO] Seeding Users table..."

    # Get User Pool ID
    local user_pool_id=$(awslocal cognito-idp list-user-pools --max-results 50 \
        --query 'UserPools[?contains(Name, `GameFlex`) || contains(Name, `gameflex`)].Id' \
        --output text | head -n1)

    if [ -z "$user_pool_id" ] || [ "$user_pool_id" = "None" ]; then
        echo "[WARN] User Pool not found, using placeholder Cognito IDs"
        user_pool_id=""
    else
        echo "[INFO] Found User Pool: $user_pool_id"
    fi

    # Get actual Cognito user IDs
    local dev_cognito_id=$(get_cognito_user_id "<EMAIL>" "$user_pool_id")
    local admin_cognito_id=$(get_cognito_user_id "<EMAIL>" "$user_pool_id")
    local john_cognito_id=$(get_cognito_user_id "<EMAIL>" "$user_pool_id")
    local jane_cognito_id=$(get_cognito_user_id "<EMAIL>" "$user_pool_id")
    local mike_cognito_id=$(get_cognito_user_id "<EMAIL>" "$user_pool_id")
    local alice_cognito_id=$(get_cognito_user_id "<EMAIL>" "$user_pool_id")
    local bob_cognito_id=$(get_cognito_user_id "<EMAIL>" "$user_pool_id")
    local charlie_cognito_id=$(get_cognito_user_id "<EMAIL>" "$user_pool_id")

    # Developer user
    put_item "Users" '{
        "id": {"S": "550e8400-e29b-41d4-a716-************"},
        "cognito_user_id": {"S": "'$dev_cognito_id'"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "developer"},
        "display_name": {"S": "GameFlex Developer"},
        "bio": {"S": "Development account for testing GameFlex features"},
        "is_active": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'

    # Admin user
    put_item "Users" '{
        "id": {"S": "550e8400-e29b-41d4-a716-************"},
        "cognito_user_id": {"S": "'$admin_cognito_id'"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "admin"},
        "display_name": {"S": "GameFlex Admin"},
        "bio": {"S": "Administrator account with full access"},
        "is_active": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'

    # Alice user
    put_item "Users" '{
        "id": {"S": "550e8400-e29b-41d4-a716-************"},
        "cognito_user_id": {"S": "'$alice_cognito_id'"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "alice_gamer"},
        "display_name": {"S": "Alice Cooper"},
        "bio": {"S": "Passionate gamer and content creator"},
        "is_active": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'

    # Bob user
    put_item "Users" '{
        "id": {"S": "550e8400-e29b-41d4-a716-************"},
        "cognito_user_id": {"S": "'$bob_cognito_id'"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "bob_streamer"},
        "display_name": {"S": "Bob Wilson"},
        "bio": {"S": "Professional esports player and streamer"},
        "is_active": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'

    # John Doe user (from backend seed data)
    put_item "Users" '{
        "id": {"S": "00000000-0000-0000-0000-000000000003"},
        "cognito_user_id": {"S": "'$john_cognito_id'"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "johndoe"},
        "display_name": {"S": "John Doe"},
        "bio": {"S": "Gaming enthusiast and content creator."},
        "is_active": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'

    # Jane Smith user (from backend seed data)
    put_item "Users" '{
        "id": {"S": "00000000-0000-0000-0000-000000000004"},
        "cognito_user_id": {"S": "'$jane_cognito_id'"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "janesmith"},
        "display_name": {"S": "Jane Smith"},
        "bio": {"S": "Professional gamer and streamer."},
        "is_active": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'

    # Mike Wilson user (from backend seed data)
    put_item "Users" '{
        "id": {"S": "00000000-0000-0000-0000-000000000005"},
        "cognito_user_id": {"S": "'$mike_cognito_id'"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "mikewilson"},
        "display_name": {"S": "Mike Wilson"},
        "bio": {"S": "Casual gamer who loves sharing gaming moments."},
        "is_active": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'

    # Charlie user
    put_item "Users" '{
        "id": {"S": "550e8400-e29b-41d4-a716-************"},
        "cognito_user_id": {"S": "'$charlie_cognito_id'"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "charlie_dev"},
        "display_name": {"S": "Charlie Brown"},
        "bio": {"S": "Game developer and indie creator"},
        "is_active": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'

    echo "[INFO] Users seeded with Cognito IDs:"
    echo "  - <EMAIL>: $dev_cognito_id"
    echo "  - <EMAIL>: $admin_cognito_id"
    echo "  - <EMAIL>: $john_cognito_id"
    echo "  - <EMAIL>: $jane_cognito_id"
    echo "  - <EMAIL>: $mike_cognito_id"
    echo "  - <EMAIL>: $alice_cognito_id"
    echo "  - <EMAIL>: $bob_cognito_id"
    echo "  - <EMAIL>: $charlie_cognito_id"
}

# Seed UserProfiles table
seed_user_profiles() {
    echo "[INFO] Seeding UserProfiles table..."
    
    put_item "UserProfiles" '{
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "first_name": {"S": "Dev"},
        "last_name": {"S": "User"},
        "country": {"S": "United States"},
        "timezone": {"S": "America/New_York"},
        "language": {"S": "en"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'
    
    put_item "UserProfiles" '{
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "first_name": {"S": "Admin"},
        "last_name": {"S": "User"},
        "country": {"S": "United States"},
        "timezone": {"S": "America/Los_Angeles"},
        "language": {"S": "en"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'

    # John Doe profile (from backend seed data)
    put_item "UserProfiles" '{
        "user_id": {"S": "00000000-0000-0000-0000-000000000003"},
        "first_name": {"S": "John"},
        "last_name": {"S": "Doe"},
        "country": {"S": "United States"},
        "timezone": {"S": "America/New_York"},
        "language": {"S": "en"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'

    # Jane Smith profile (from backend seed data)
    put_item "UserProfiles" '{
        "user_id": {"S": "00000000-0000-0000-0000-000000000004"},
        "first_name": {"S": "Jane"},
        "last_name": {"S": "Smith"},
        "country": {"S": "United States"},
        "timezone": {"S": "America/Los_Angeles"},
        "language": {"S": "en"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'

    # Mike Wilson profile (from backend seed data)
    put_item "UserProfiles" '{
        "user_id": {"S": "00000000-0000-0000-0000-000000000005"},
        "first_name": {"S": "Mike"},
        "last_name": {"S": "Wilson"},
        "country": {"S": "United States"},
        "timezone": {"S": "America/Chicago"},
        "language": {"S": "en"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'

    put_item "UserProfiles" '{
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "first_name": {"S": "Alice"},
        "last_name": {"S": "Cooper"},
        "country": {"S": "Canada"},
        "timezone": {"S": "America/Toronto"},
        "language": {"S": "en"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'
    
    put_item "UserProfiles" '{
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "first_name": {"S": "Bob"},
        "last_name": {"S": "Wilson"},
        "country": {"S": "United Kingdom"},
        "timezone": {"S": "Europe/London"},
        "language": {"S": "en"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'
    
    put_item "UserProfiles" '{
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "first_name": {"S": "Charlie"},
        "last_name": {"S": "Brown"},
        "country": {"S": "Australia"},
        "timezone": {"S": "Australia/Sydney"},
        "language": {"S": "en"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'
}

# Seed Channels table
seed_channels() {
    echo "[INFO] Seeding Channels table..."
    
    put_item "Channels" '{
        "id": {"S": "660e8400-e29b-41d4-a716-************"},
        "name": {"S": "Fortnite"},
        "description": {"S": "Everything about Fortnite - tips, tricks, and epic moments"},
        "owner_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "is_public": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "member_count": {"N": "150"},
        "post_count": {"N": "45"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'
    
    put_item "Channels" '{
        "id": {"S": "660e8400-e29b-41d4-a716-************"},
        "name": {"S": "Valorant"},
        "description": {"S": "Tactical FPS discussions, strategies, and highlights"},
        "owner_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "is_public": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "member_count": {"N": "200"},
        "post_count": {"N": "67"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'
    
    put_item "Channels" '{
        "id": {"S": "660e8400-e29b-41d4-a716-************"},
        "name": {"S": "Minecraft"},
        "description": {"S": "Creative builds, survival tips, and community projects"},
        "owner_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "is_public": {"BOOL": true},
        "is_verified": {"BOOL": false},
        "member_count": {"N": "300"},
        "post_count": {"N": "89"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'
    
    put_item "Channels" '{
        "id": {"S": "660e8400-e29b-41d4-a716-************"},
        "name": {"S": "League of Legends"},
        "description": {"S": "MOBA strategies, champion guides, and esports news"},
        "owner_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "is_public": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "member_count": {"N": "500"},
        "post_count": {"N": "123"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'
    
    put_item "Channels" '{
        "id": {"S": "660e8400-e29b-41d4-a716-************"},
        "name": {"S": "Indie Games"},
        "description": {"S": "Discover and discuss amazing indie games"},
        "owner_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "is_public": {"BOOL": true},
        "is_verified": {"BOOL": false},
        "member_count": {"N": "75"},
        "post_count": {"N": "34"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }'
}

# Seed Media table
seed_media() {
    echo "[INFO] Seeding Media table..."

    # Media for first post (John Doe's COD screenshot)
    put_item "Media" '{
        "id": {"S": "30000000-0000-0000-0000-000000000001"},
        "type": {"S": "image"},
        "location": {"S": "user"},
        "name": {"S": "cod_screenshot"},
        "extension": {"S": "jpg"},
        "owner_id": {"S": "00000000-0000-0000-0000-000000000003"},
        "bucket_location": {"S": "s3"},
        "bucket_name": {"S": "gameflex-media-development"},
        "bucket_permission": {"S": "public"},
        "s3_url": {"S": "http://localhost:4566/gameflex-media-development/user/00000000-0000-0000-0000-000000000003/cod_screenshot.jpg"},
        "width": {"N": "1920"},
        "height": {"N": "1080"},
        "size_bytes": {"N": "245760"},
        "mime_type": {"S": "image/jpeg"},
        "created_at": {"S": "2024-12-28T14:30:00Z"},
        "updated_at": {"S": "2024-12-28T14:30:00Z"}
    }'

    # Media for second post (Jane Smith's Diablo screenshot)
    put_item "Media" '{
        "id": {"S": "30000000-0000-0000-0000-000000000002"},
        "type": {"S": "image"},
        "location": {"S": "user"},
        "name": {"S": "diablo_screenshot"},
        "extension": {"S": "webp"},
        "owner_id": {"S": "00000000-0000-0000-0000-000000000004"},
        "bucket_location": {"S": "s3"},
        "bucket_name": {"S": "gameflex-media-development"},
        "bucket_permission": {"S": "public"},
        "s3_url": {"S": "http://localhost:4566/gameflex-media-development/user/00000000-0000-0000-0000-000000000004/diablo_screenshot.webp"},
        "width": {"N": "1920"},
        "height": {"N": "1080"},
        "size_bytes": {"N": "189440"},
        "mime_type": {"S": "image/webp"},
        "created_at": {"S": "2024-12-27T16:45:00Z"},
        "updated_at": {"S": "2024-12-27T16:45:00Z"}
    }'
}

# Clear Posts table
clear_posts() {
    echo "[INFO] Clearing Posts table..."

    # Get all post IDs
    local post_ids=$(awslocal dynamodb scan --table-name Posts --query 'Items[].id.S' --output text)

    if [ -n "$post_ids" ]; then
        for post_id in $post_ids; do
            echo "[INFO] Deleting post: $post_id"
            awslocal dynamodb delete-item --table-name Posts --key '{"id":{"S":"'$post_id'"}}'
        done
        echo "[INFO] All posts cleared"
    else
        echo "[INFO] Posts table is already empty"
    fi
}

# Seed Posts table
seed_posts() {
    echo "[INFO] Seeding Posts table..."

    # Clear existing posts first
    clear_posts

    # Get User Pool ID
    local user_pool_id=$(awslocal cognito-idp list-user-pools --max-results 50 \
        --query 'UserPools[?contains(Name, `GameFlex`) || contains(Name, `gameflex`)].Id' \
        --output text 2>/dev/null | head -n1)

    if [ -z "$user_pool_id" ] || [ "$user_pool_id" = "None" ]; then
        echo "[WARN] User Pool not found, posts will use placeholder Cognito IDs"
        user_pool_id=""
    else
        echo "[INFO] Found User Pool: $user_pool_id"
    fi

    # Get Cognito user IDs for proper mapping
    local john_cognito_id=$(get_cognito_user_id "<EMAIL>" "$user_pool_id")
    local jane_cognito_id=$(get_cognito_user_id "<EMAIL>" "$user_pool_id")

    # First post - John Doe's COD screenshot
    put_item "Posts" '{
        "id": {"S": "20000000-0000-0000-0000-000000000001"},
        "author_id": {"S": "00000000-0000-0000-0000-000000000003"},
        "cognito_user_id": {"S": "'$john_cognito_id'"},
        "content": {"S": "Just had an epic Call of Duty session! Check out this clutch moment 🎮🔥"},
        "media_id": {"S": "cod_screenshot.jpg"},
        "media_type": {"S": "image"},
        "s3_bucket": {"S": "gameflex-media-development"},
        "s3_key": {"S": "user/00000000-0000-0000-0000-000000000003/cod_screenshot.jpg"},
        "like_count": {"N": "12"},
        "comment_count": {"N": "4"},
        "is_active": {"BOOL": true},
        "created_at": {"S": "2024-12-28T14:30:00Z"},
        "updated_at": {"S": "2024-12-28T14:30:00Z"}
    }'

    # Second post - Jane Smith's Diablo screenshot
    put_item "Posts" '{
        "id": {"S": "20000000-0000-0000-0000-000000000002"},
        "author_id": {"S": "00000000-0000-0000-0000-000000000004"},
        "cognito_user_id": {"S": "'$jane_cognito_id'"},
        "content": {"S": "Finally defeated this boss in Diablo! The loot was totally worth the grind 💀⚔️"},
        "media_id": {"S": "diablo_screenshot.webp"},
        "media_type": {"S": "image"},
        "s3_bucket": {"S": "gameflex-media-development"},
        "s3_key": {"S": "user/00000000-0000-0000-0000-000000000004/diablo_screenshot.webp"},
        "like_count": {"N": "18"},
        "comment_count": {"N": "6"},
        "is_active": {"BOOL": true},
        "created_at": {"S": "2024-12-27T16:45:00Z"},
        "updated_at": {"S": "2024-12-27T16:45:00Z"}
    }'

    echo "[INFO] Posts seeded with Cognito IDs:"
    echo "  - <EMAIL>: $john_cognito_id"
    echo "  - <EMAIL>: $jane_cognito_id"
}

# Seed Comments table
seed_comments() {
    echo "[INFO] Seeding Comments table..."

    # Comments for first post (John's COD screenshot)
    put_item "Comments" '{
        "id": {"S": "40000000-0000-0000-0000-000000000001"},
        "post_id": {"S": "20000000-0000-0000-0000-000000000001"},
        "user_id": {"S": "00000000-0000-0000-0000-000000000004"},
        "content": {"S": "Nice clutch! What loadout were you using?"},
        "like_count": {"N": "3"},
        "is_active": {"BOOL": true},
        "created_at": {"S": "2024-12-28T15:15:00Z"},
        "updated_at": {"S": "2024-12-28T15:15:00Z"}
    }'

    put_item "Comments" '{
        "id": {"S": "40000000-0000-0000-0000-000000000002"},
        "post_id": {"S": "20000000-0000-0000-0000-000000000001"},
        "user_id": {"S": "00000000-0000-0000-0000-000000000005"},
        "content": {"S": "That was insane! 🔥"},
        "like_count": {"N": "1"},
        "is_active": {"BOOL": true},
        "created_at": {"S": "2024-12-28T16:20:00Z"},
        "updated_at": {"S": "2024-12-28T16:20:00Z"}
    }'

    put_item "Comments" '{
        "id": {"S": "40000000-0000-0000-0000-000000000003"},
        "post_id": {"S": "20000000-0000-0000-0000-000000000001"},
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "content": {"S": "Great gameplay! Keep it up 👍"},
        "like_count": {"N": "2"},
        "is_active": {"BOOL": true},
        "created_at": {"S": "2024-12-28T17:30:00Z"},
        "updated_at": {"S": "2024-12-28T17:30:00Z"}
    }'

    put_item "Comments" '{
        "id": {"S": "40000000-0000-0000-0000-000000000004"},
        "post_id": {"S": "20000000-0000-0000-0000-000000000001"},
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "content": {"S": "Which map is this? Looks intense!"},
        "like_count": {"N": "0"},
        "is_active": {"BOOL": true},
        "created_at": {"S": "2024-12-28T18:45:00Z"},
        "updated_at": {"S": "2024-12-28T18:45:00Z"}
    }'

    # Comments for second post (Jane's Diablo screenshot)
    put_item "Comments" '{
        "id": {"S": "40000000-0000-0000-0000-000000000005"},
        "post_id": {"S": "20000000-0000-0000-0000-000000000002"},
        "user_id": {"S": "00000000-0000-0000-0000-000000000003"},
        "content": {"S": "Congrats! What difficulty level?"},
        "like_count": {"N": "2"},
        "is_active": {"BOOL": true},
        "created_at": {"S": "2024-12-27T17:30:00Z"},
        "updated_at": {"S": "2024-12-27T17:30:00Z"}
    }'

    put_item "Comments" '{
        "id": {"S": "40000000-0000-0000-0000-000000000006"},
        "post_id": {"S": "20000000-0000-0000-0000-000000000002"},
        "user_id": {"S": "00000000-0000-0000-0000-000000000005"},
        "content": {"S": "The loot looks amazing! 💎"},
        "like_count": {"N": "4"},
        "is_active": {"BOOL": true},
        "created_at": {"S": "2024-12-27T18:15:00Z"},
        "updated_at": {"S": "2024-12-27T18:15:00Z"}
    }'

    put_item "Comments" '{
        "id": {"S": "40000000-0000-0000-0000-000000000007"},
        "post_id": {"S": "20000000-0000-0000-0000-000000000002"},
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "content": {"S": "Epic boss fight! How long did it take?"},
        "like_count": {"N": "1"},
        "is_active": {"BOOL": true},
        "created_at": {"S": "2024-12-27T19:00:00Z"},
        "updated_at": {"S": "2024-12-27T19:00:00Z"}
    }'

    put_item "Comments" '{
        "id": {"S": "40000000-0000-0000-0000-000000000008"},
        "post_id": {"S": "20000000-0000-0000-0000-000000000002"},
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "content": {"S": "I need to try this boss next! Any tips?"},
        "like_count": {"N": "3"},
        "is_active": {"BOOL": true},
        "created_at": {"S": "2024-12-27T20:30:00Z"},
        "updated_at": {"S": "2024-12-27T20:30:00Z"}
    }'

    put_item "Comments" '{
        "id": {"S": "40000000-0000-0000-0000-000000000009"},
        "post_id": {"S": "20000000-0000-0000-0000-000000000002"},
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "content": {"S": "Diablo never gets old! 🔥⚔️"},
        "like_count": {"N": "2"},
        "is_active": {"BOOL": true},
        "created_at": {"S": "2024-12-27T21:15:00Z"},
        "updated_at": {"S": "2024-12-27T21:15:00Z"}
    }'

    put_item "Comments" '{
        "id": {"S": "40000000-0000-0000-0000-000000000010"},
        "post_id": {"S": "20000000-0000-0000-0000-000000000002"},
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "content": {"S": "What class are you playing?"},
        "like_count": {"N": "0"},
        "is_active": {"BOOL": true},
        "created_at": {"S": "2024-12-27T22:00:00Z"},
        "updated_at": {"S": "2024-12-27T22:00:00Z"}
    }'
}

# Seed Likes table
seed_likes() {
    echo "[INFO] Seeding Likes table..."

    # Likes for first post (John's COD screenshot) - 12 likes total
    put_item "Likes" '{
        "post_id": {"S": "20000000-0000-0000-0000-000000000001"},
        "user_id": {"S": "00000000-0000-0000-0000-000000000004"},
        "created_at": {"S": "2024-12-28T14:35:00Z"}
    }'

    put_item "Likes" '{
        "post_id": {"S": "20000000-0000-0000-0000-000000000001"},
        "user_id": {"S": "00000000-0000-0000-0000-000000000005"},
        "created_at": {"S": "2024-12-28T14:40:00Z"}
    }'

    put_item "Likes" '{
        "post_id": {"S": "20000000-0000-0000-0000-000000000001"},
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "created_at": {"S": "2024-12-28T14:45:00Z"}
    }'

    put_item "Likes" '{
        "post_id": {"S": "20000000-0000-0000-0000-000000000001"},
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "created_at": {"S": "2024-12-28T15:00:00Z"}
    }'

    put_item "Likes" '{
        "post_id": {"S": "20000000-0000-0000-0000-000000000001"},
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "created_at": {"S": "2024-12-28T15:30:00Z"}
    }'

    put_item "Likes" '{
        "post_id": {"S": "20000000-0000-0000-0000-000000000001"},
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "created_at": {"S": "2024-12-28T16:00:00Z"}
    }'

    put_item "Likes" '{
        "post_id": {"S": "20000000-0000-0000-0000-000000000001"},
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "created_at": {"S": "2024-12-28T16:30:00Z"}
    }'

    # Likes for second post (Jane's Diablo screenshot) - 18 likes total
    put_item "Likes" '{
        "post_id": {"S": "20000000-0000-0000-0000-000000000002"},
        "user_id": {"S": "00000000-0000-0000-0000-000000000003"},
        "created_at": {"S": "2024-12-27T16:50:00Z"}
    }'

    put_item "Likes" '{
        "post_id": {"S": "20000000-0000-0000-0000-000000000002"},
        "user_id": {"S": "00000000-0000-0000-0000-000000000005"},
        "created_at": {"S": "2024-12-27T17:00:00Z"}
    }'

    put_item "Likes" '{
        "post_id": {"S": "20000000-0000-0000-0000-000000000002"},
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "created_at": {"S": "2024-12-27T17:15:00Z"}
    }'

    put_item "Likes" '{
        "post_id": {"S": "20000000-0000-0000-0000-000000000002"},
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "created_at": {"S": "2024-12-27T17:30:00Z"}
    }'

    put_item "Likes" '{
        "post_id": {"S": "20000000-0000-0000-0000-000000000002"},
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "created_at": {"S": "2024-12-27T18:00:00Z"}
    }'

    put_item "Likes" '{
        "post_id": {"S": "20000000-0000-0000-0000-000000000002"},
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "created_at": {"S": "2024-12-27T18:30:00Z"}
    }'

    put_item "Likes" '{
        "post_id": {"S": "20000000-0000-0000-0000-000000000002"},
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "created_at": {"S": "2024-12-27T19:00:00Z"}
    }'
}

# Main execution
echo "[DYNAMODB] Starting data seeding..."

seed_users
seed_user_profiles
seed_channels
seed_media
seed_posts
seed_comments
seed_likes

echo "[DYNAMODB] Complete data seeded successfully!"
echo "[INFO] Seeded 2 posts with media, comments, and likes from initialized users"
