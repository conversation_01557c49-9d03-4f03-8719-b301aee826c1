#!/bin/bash

# GameFlex Lambda Functions Deployment Script
# This script deploys pre-built Lambda functions for the GameFlex application
# Runs automatically after DynamoDB and S3 setup
# NOTE: This script now expects Lambda packages to be pre-built in /opt/packages

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${BLUE}[LAMBDA]${NC} $1"
}

log_header "Deploying Lambda functions for GameFlex..."

# Function to deploy a pre-built Lambda function
deploy_lambda_function() {
    local function_name=$1
    local package_file=$2
    local handler=$3
    local description=$4

    log_info "Deploying Lambda function: $function_name"

    # Check if package file exists
    if [ ! -f "$package_file" ]; then
        log_warn "Lambda package not found: $package_file"
        return 0
    fi

    log_info "Using pre-built package: $(basename "$package_file")"

    # Get User Pool information for environment variables
    local user_pool_id=$(awslocal cognito-idp list-user-pools --max-results 50 \
        --query 'UserPools[?contains(Name, `GameFlex`) || contains(Name, `gameflex`)].Id' \
        --output text 2>/dev/null | head -n1)

    local user_pool_client_id=""
    if [ -n "$user_pool_id" ] && [ "$user_pool_id" != "None" ]; then
        user_pool_client_id=$(awslocal cognito-idp list-user-pool-clients --user-pool-id "$user_pool_id" \
            --query 'UserPoolClients[0].ClientId' \
            --output text 2>/dev/null || echo "")
    fi

    # Check if function exists
    local function_exists=$(awslocal lambda get-function --function-name "$function_name" 2>/dev/null || echo "")

    if [ -n "$function_exists" ]; then
        log_info "Updating existing Lambda function: $function_name"

        # Update function code
        awslocal lambda update-function-code \
            --function-name "$function_name" \
            --zip-file "fileb://$package_file" > /dev/null

        # Update function configuration
        awslocal lambda update-function-configuration \
            --function-name "$function_name" \
            --handler "$handler" \
            --environment "Variables={COGNITO_USER_POOL_ID=$user_pool_id,COGNITO_USER_POOL_CLIENT_ID=$user_pool_client_id,AWS_ENDPOINT_URL=http://host.docker.internal:45660,AWS_DEFAULT_REGION=us-east-1}" > /dev/null
    else
        log_info "Creating new Lambda function: $function_name"

        # Create function
        awslocal lambda create-function \
            --function-name "$function_name" \
            --runtime nodejs18.x \
            --role "arn:aws:iam::000000000000:role/lambda-role" \
            --handler "$handler" \
            --zip-file "fileb://$package_file" \
            --description "$description" \
            --environment "Variables={COGNITO_USER_POOL_ID=$user_pool_id,COGNITO_USER_POOL_CLIENT_ID=$user_pool_client_id,AWS_ENDPOINT_URL=http://host.docker.internal:45660,AWS_DEFAULT_REGION=us-east-1}" > /dev/null
    fi

    log_info "Successfully deployed Lambda function: $function_name"
}

# Set AWS environment variables for LocalStack
export AWS_ACCESS_KEY_ID=test
export AWS_SECRET_ACCESS_KEY=test
export AWS_DEFAULT_REGION=us-east-1

# Wait for Lambda service to be available
log_info "Waiting for Lambda service to be available..."
timeout=60
counter=0
while [ $counter -lt $timeout ]; do
    if awslocal lambda list-functions > /dev/null 2>&1; then
        log_info "Lambda service is available"
        break
    fi
    sleep 2
    counter=$((counter + 2))
    if [ $counter -ge $timeout ]; then
        log_error "Lambda service not available within $timeout seconds"
        log_info "Attempting to continue anyway..."
        break
    fi
done

# Ensure IAM role exists for Lambda functions
log_info "Ensuring Lambda execution role exists..."
role_exists=$(awslocal iam get-role --role-name lambda-role 2>/dev/null || echo "")
if [ -z "$role_exists" ]; then
    log_info "Creating Lambda execution role..."

    # Create trust policy
    cat > /tmp/lambda-trust-policy.json << EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "Service": "lambda.amazonaws.com"
            },
            "Action": "sts:AssumeRole"
        }
    ]
}
EOF

    awslocal iam create-role \
        --role-name lambda-role \
        --assume-role-policy-document file:///tmp/lambda-trust-policy.json > /dev/null

    awslocal iam attach-role-policy \
        --role-name lambda-role \
        --policy-arn arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole > /dev/null

    rm -f /tmp/lambda-trust-policy.json
    log_info "Lambda execution role created"
else
    log_info "Lambda execution role already exists"
fi

# Deploy Lambda functions using pre-built packages
log_info "Starting Lambda function deployment..."

# Check if packages directory exists
if [ ! -d "/opt/packages" ]; then
    log_error "Packages directory not found: /opt/packages"
    log_error "Lambda functions must be built before deployment"
    exit 1
fi

# Deploy auth function
if [ -f "/opt/packages/gameflex-auth-development.zip" ]; then
    deploy_lambda_function "gameflex-auth-development" "/opt/packages/gameflex-auth-development.zip" "handler.handler" "GameFlex authentication functions"
else
    log_warn "Auth function package not found"
fi

# Deploy posts function
if [ -f "/opt/packages/gameflex-posts-development.zip" ]; then
    deploy_lambda_function "gameflex-posts-development" "/opt/packages/gameflex-posts-development.zip" "handler.handler" "GameFlex posts management functions"
else
    log_warn "Posts function package not found"
fi

# Deploy users function
if [ -f "/opt/packages/gameflex-users-development.zip" ]; then
    deploy_lambda_function "gameflex-users-development" "/opt/packages/gameflex-users-development.zip" "handler.handler" "GameFlex user management functions"
else
    log_warn "Users function package not found"
fi

# Deploy media function
if [ -f "/opt/packages/gameflex-media-development.zip" ]; then
    deploy_lambda_function "gameflex-media-development" "/opt/packages/gameflex-media-development.zip" "handler.handler" "GameFlex media management functions"
else
    log_warn "Media function package not found"
fi

log_header "All Lambda functions deployed successfully!"
log_info "Lambda functions are ready with proper TypeScript implementations"
