# Docker Compose file for GameFlex AWS Backend

services:
  # Lambda function builder service
  lambda-builder:
    build:
      context: ./docker
      dockerfile: lambda-builder.Dockerfile
    container_name: gameflex-lambda-builder
    volumes:
      - "./lambda-functions:/workspace/lambda-functions"
      - "./packages:/workspace/packages"
      - "./docker/build-all-lambdas.sh:/usr/local/bin/build-all-lambdas.sh:ro"
    environment:
      - NODE_ENV=production
    networks:
      - gameflex-aws
    command: ["/usr/local/bin/build-all-lambdas.sh"]
    profiles:
      - build

  # AWS infrastructure initializer service
  aws-initializer:
    build:
      context: ./docker
      dockerfile: deployer.Dockerfile
    container_name: gameflex-aws-initializer
    depends_on:
      localstack:
        condition: service_healthy
    volumes:
      - "./cloudformation:/workspace/cloudformation"
      - "./packages:/workspace/packages"
      - "./docker/init-gameflex.sh:/usr/local/bin/init-gameflex.sh:ro"
      - "./docker/deploy-infrastructure.sh:/usr/local/bin/deploy-infrastructure.sh:ro"
      - "./docker/build-all-lambdas.sh:/usr/local/bin/build-all-lambdas.sh:ro"
      - "./init:/workspace/init"
      - "./lambda-functions:/workspace/lambda-functions"
    environment:
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
      - AWS_DEFAULT_REGION=us-east-1
      - AWS_ENDPOINT_URL=http://localstack:4566
      - LOCALSTACK_HOST=localstack:4566
      - ENVIRONMENT=development
      - PROJECT_NAME=gameflex
    networks:
      - gameflex-aws
    command: ["/usr/local/bin/init-gameflex.sh"]
    profiles:
      - init

  # LocalStack Pro service
  localstack:
    container_name: "${LOCALSTACK_DOCKER_NAME:-gameflex-localstack}"
    image: localstack/localstack-pro:latest
    ports:
      - "127.0.0.1:4566:4566"            # LocalStack Gateway
      - "127.0.0.1:4510-4559:4510-4559"  # external services port range
      - "127.0.0.1:443:443"              # LocalStack HTTPS Gateway (Pro)
      - "45660:4566"                     # Keep existing port mapping for compatibility
    env_file:
      - .env
    environment:
      # Activate LocalStack Pro
      - LOCALSTACK_AUTH_TOKEN=${LOCALSTACK_AUTH_TOKEN:?}

      # LocalStack configuration
      - DEBUG=${DEBUG:-1}
      - PERSISTENCE=${PERSISTENCE:-1}
      - LAMBDA_EXECUTOR=docker-reuse
      - DOCKER_HOST=unix:///var/run/docker.sock
      - HOST_TMP_FOLDER=${TMPDIR:-/tmp}/localstack

      # AWS Configuration
      - AWS_DEFAULT_REGION=us-east-1
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test

      # Enable AWS services
      - SERVICES=cognito-idp,cognito-identity,dynamodb,lambda,apigateway,s3,cloudformation,iam,sts,logs,secretsmanager,ssm

      # S3 configuration
      - S3_SKIP_SIGNATURE_VALIDATION=1

      # Lambda configuration
      - LAMBDA_RUNTIME_ENVIRONMENT_TIMEOUT=60
      - LAMBDA_REMOVE_CONTAINERS=true
      - LAMBDA_DOCKER_NETWORK=gameflex-aws

      # API Gateway configuration
      - GATEWAY_LISTEN=0.0.0.0:4566

      # Enable init hooks
      - INIT_SCRIPTS_PATH=/etc/localstack/init/ready.d

    volumes:
      - "${LOCALSTACK_VOLUME_DIR:-./volume}:/var/lib/localstack"
      - "/var/run/docker.sock:/var/run/docker.sock"
      - "./init:/etc/localstack/init/ready.d"
      - "./lambda-functions:/opt/lambda-functions"
      - "./cloudformation:/opt/cloudformation"
      - "./packages:/opt/packages"
      - "./assets:/opt/assets"
    networks:
      - gameflex-aws
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4566/_localstack/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

networks:
  gameflex-aws:
    driver: bridge
    name: gameflex-aws
