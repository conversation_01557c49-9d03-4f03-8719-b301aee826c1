[{"ParameterKey": "Environment", "ParameterValue": "qa"}, {"ParameterKey": "ProjectName", "ParameterValue": "gameflex"}, {"ParameterKey": "DomainName", "ParameterValue": "qa.gameflex.io"}, {"ParameterKey": "ApiDomainName", "ParameterValue": "qa.api.gameflex.io"}, {"ParameterKey": "CertificateArn", "ParameterValue": "arn:aws:acm:us-east-1:ACCOUNT_ID:certificate/CERTIFICATE_ID"}, {"ParameterKey": "EnableCloudFront", "ParameterValue": "true"}, {"ParameterKey": "EnableWAF", "ParameterValue": "false"}, {"ParameterKey": "EnableXRay", "ParameterValue": "true"}, {"ParameterKey": "LambdaMemorySize", "ParameterValue": "512"}, {"ParameterKey": "LambdaTimeout", "ParameterValue": "30"}, {"ParameterKey": "S3BucketVersioning", "ParameterValue": "Enabled"}, {"ParameterKey": "S3BucketEncryption", "ParameterValue": "true"}, {"ParameterKey": "EnableS3AccessLogging", "ParameterValue": "true"}, {"ParameterKey": "CognitoPasswordMinLength", "ParameterValue": "8"}, {"ParameterKey": "CognitoPasswordRequireUppercase", "ParameterValue": "true"}, {"ParameterKey": "CognitoPasswordRequireLowercase", "ParameterValue": "true"}, {"ParameterKey": "CognitoPasswordRequireNumbers", "ParameterValue": "true"}, {"ParameterKey": "CognitoPasswordRequireSymbols", "ParameterValue": "true"}, {"ParameterKey": "CognitoMfaConfiguration", "ParameterValue": "OPTIONAL"}, {"ParameterKey": "ApiGatewayThrottleBurstLimit", "ParameterValue": "500"}, {"ParameterKey": "ApiGatewayThrottleRateLimit", "ParameterValue": "250"}, {"ParameterKey": "EnableApiGatewayLogging", "ParameterValue": "true"}, {"ParameterKey": "ApiGatewayLogLevel", "ParameterValue": "INFO"}, {"ParameterKey": "EnableDetailedMetrics", "ParameterValue": "true"}, {"ParameterKey": "AlertingEmail", "ParameterValue": "<EMAIL>"}, {"ParameterKey": "Enable<PERSON><PERSON><PERSON>", "ParameterValue": "true"}, {"ParameterKey": "LogRetentionDays", "ParameterValue": "14"}, {"ParameterKey": "EnableVPCEndpoints", "ParameterValue": "false"}, {"ParameterKey": "VPCCidr", "ParameterValue": "10.1.0.0/16"}, {"ParameterKey": "PublicSubnet1Cidr", "ParameterValue": "10.1.1.0/24"}, {"ParameterKey": "PublicSubnet2Cidr", "ParameterValue": "10.1.2.0/24"}, {"ParameterKey": "PrivateSubnet1Cidr", "ParameterValue": "10.1.3.0/24"}, {"ParameterKey": "PrivateSubnet2Cidr", "ParameterValue": "10.1.4.0/24"}]