AWSTemplateFormatVersion: "2010-09-09"
Description: "GameFlex AWS Infrastructure - Unified Template for All Environments"

Parameters:
  Environment:
    Type: String
    Default: development
    AllowedValues:
      - development
      - staging
      - production
    Description: Environment name

  ProjectName:
    Type: String
    Default: gameflex
    Description: Project name for resource naming

  DomainName:
    Type: String
    Default: localhost
    Description: Domain name for the application

  ApiDomainName:
    Type: String
    Default: localhost
    Description: API domain name

  CertificateArn:
    Type: String
    Default: ""
    Description: SSL certificate ARN

  EnableCloudFront:
    Type: String
    Default: "false"
    AllowedValues: ["true", "false"]
    Description: Enable CloudFront distribution

  EnableWAF:
    Type: String
    Default: "false"
    AllowedValues: ["true", "false"]
    Description: Enable Web Application Firewall

  EnableXRay:
    Type: String
    Default: "false"
    AllowedValues: ["true", "false"]
    Description: Enable X-Ray tracing

  LambdaMemorySize:
    Type: Number
    Default: 256
    MinValue: 128
    MaxValue: 10240
    Description: Lambda function memory size

  LambdaTimeout:
    Type: Number
    Default: 30
    MinValue: 1
    MaxValue: 900
    Description: Lambda function timeout

  S3BucketVersioning:
    Type: String
    Default: Suspended
    AllowedValues: [Enabled, Suspended]
    Description: S3 bucket versioning configuration

  S3BucketEncryption:
    Type: String
    Default: "false"
    AllowedValues: ["true", "false"]
    Description: Enable S3 bucket encryption

  EnableS3AccessLogging:
    Type: String
    Default: "false"
    AllowedValues: ["true", "false"]
    Description: Enable S3 access logging

  CognitoPasswordMinLength:
    Type: Number
    Default: 8
    MinValue: 6
    MaxValue: 99
    Description: Minimum password length for Cognito

  CognitoPasswordRequireUppercase:
    Type: String
    Default: "true"
    AllowedValues: ["true", "false"]
    Description: Require uppercase letters in passwords

  CognitoPasswordRequireLowercase:
    Type: String
    Default: "true"
    AllowedValues: ["true", "false"]
    Description: Require lowercase letters in passwords

  CognitoPasswordRequireNumbers:
    Type: String
    Default: "true"
    AllowedValues: ["true", "false"]
    Description: Require numbers in passwords

  CognitoPasswordRequireSymbols:
    Type: String
    Default: "false"
    AllowedValues: ["true", "false"]
    Description: Require symbols in passwords

  CognitoMfaConfiguration:
    Type: String
    Default: OFF
    AllowedValues: [OFF, ON, OPTIONAL]
    Description: MFA configuration for Cognito

  ApiGatewayThrottleBurstLimit:
    Type: Number
    Default: 200
    MinValue: 0
    MaxValue: 5000
    Description: API Gateway throttle burst limit

  ApiGatewayThrottleRateLimit:
    Type: Number
    Default: 100
    MinValue: 0
    MaxValue: 10000
    Description: API Gateway throttle rate limit

  EnableApiGatewayLogging:
    Type: String
    Default: "false"
    AllowedValues: ["true", "false"]
    Description: Enable API Gateway logging

  ApiGatewayLogLevel:
    Type: String
    Default: INFO
    AllowedValues: [OFF, ERROR, INFO]
    Description: API Gateway log level

  EnableDetailedMetrics:
    Type: String
    Default: "false"
    AllowedValues: ["true", "false"]
    Description: Enable detailed metrics

  AlertingEmail:
    Type: String
    Default: <EMAIL>
    Description: Email for alerts

  EnableAlerts:
    Type: String
    Default: "false"
    AllowedValues: ["true", "false"]
    Description: Enable alerting

  LogRetentionDays:
    Type: Number
    Default: 7
    MinValue: 1
    MaxValue: 3653
    Description: Log retention period in days

  EnableVPCEndpoints:
    Type: String
    Default: "false"
    AllowedValues: ["true", "false"]
    Description: Enable VPC endpoints

  VPCCidr:
    Type: String
    Default: 10.0.0.0/16
    Description: CIDR block for VPC

  PublicSubnet1Cidr:
    Type: String
    Default: ********/24
    Description: CIDR block for public subnet 1

  PublicSubnet2Cidr:
    Type: String
    Default: ********/24
    Description: CIDR block for public subnet 2

  PrivateSubnet1Cidr:
    Type: String
    Default: ********/24
    Description: CIDR block for private subnet 1

  PrivateSubnet2Cidr:
    Type: String
    Default: ********/24
    Description: CIDR block for private subnet 2

Conditions:
  IsDevelopment: !Equals [!Ref Environment, development]
  IsProduction: !Equals [!Ref Environment, production]

Resources:
  # S3 Buckets
  MediaBucket:
    Type: AWS::S3::Bucket
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      BucketName: !Sub "${ProjectName}-media-${Environment}"
      PublicAccessBlockConfiguration:
        BlockPublicAcls: false
        BlockPublicPolicy: false
        IgnorePublicAcls: false
        RestrictPublicBuckets: false
      CorsConfiguration:
        CorsRules:
          - AllowedHeaders:
              - "*"
            AllowedMethods:
              - GET
              - PUT
              - POST
              - DELETE
              - HEAD
            AllowedOrigins:
              - "*"
            MaxAge: 3000

  AvatarsBucket:
    Type: AWS::S3::Bucket
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      BucketName: !Sub "${ProjectName}-avatars-${Environment}"
      PublicAccessBlockConfiguration:
        BlockPublicAcls: false
        BlockPublicPolicy: false
        IgnorePublicAcls: false
        RestrictPublicBuckets: false
      CorsConfiguration:
        CorsRules:
          - AllowedHeaders:
              - "*"
            AllowedMethods:
              - GET
              - PUT
              - POST
              - DELETE
              - HEAD
            AllowedOrigins:
              - "*"
            MaxAge: 3000

  TempBucket:
    Type: AWS::S3::Bucket
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      BucketName: !Sub "${ProjectName}-temp-${Environment}"
      LifecycleConfiguration:
        Rules:
          - Id: DeleteTempFiles
            Status: Enabled
            ExpirationInDays: 1

  LambdaCodeBucket:
    Type: AWS::S3::Bucket
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      BucketName: !Sub "${ProjectName}-lambda-code-${Environment}"
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true

  # DynamoDB Tables
  UsersTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      TableName: !Sub "${ProjectName}-${Environment}-Users"
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: email
          AttributeType: S
        - AttributeName: username
          AttributeType: S
        - AttributeName: cognito_user_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: EmailIndex
          KeySchema:
            - AttributeName: email
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: UsernameIndex
          KeySchema:
            - AttributeName: username
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: CognitoUserIdIndex
          KeySchema:
            - AttributeName: cognito_user_id
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      BillingMode: PAY_PER_REQUEST

  PostsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      TableName: !Sub "${ProjectName}-${Environment}-Posts"
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST

  MediaTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      TableName: !Sub "${ProjectName}-${Environment}-Media"
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST

  UserProfilesTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      TableName: !Sub "${ProjectName}-${Environment}-UserProfiles"
      AttributeDefinitions:
        - AttributeName: user_id
          AttributeType: S
      KeySchema:
        - AttributeName: user_id
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST

  CommentsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      TableName: !Sub "${ProjectName}-${Environment}-Comments"
      AttributeDefinitions:
        - AttributeName: post_id
          AttributeType: S
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: post_id
          KeyType: HASH
        - AttributeName: id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST

  LikesTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      TableName: !Sub "${ProjectName}-${Environment}-Likes"
      AttributeDefinitions:
        - AttributeName: post_id
          AttributeType: S
        - AttributeName: user_id
          AttributeType: S
      KeySchema:
        - AttributeName: post_id
          KeyType: HASH
        - AttributeName: user_id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST

  FollowsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      TableName: !Sub "${ProjectName}-${Environment}-Follows"
      AttributeDefinitions:
        - AttributeName: follower_id
          AttributeType: S
        - AttributeName: following_id
          AttributeType: S
      KeySchema:
        - AttributeName: follower_id
          KeyType: HASH
        - AttributeName: following_id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST

  # Cognito User Pool
  UserPool:
    Type: AWS::Cognito::UserPool
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      UserPoolName: !Sub "${ProjectName}-users-${Environment}"
      AutoVerifiedAttributes:
        - email
      UsernameAttributes:
        - email
      Policies:
        PasswordPolicy:
          MinimumLength: 8
          RequireUppercase: true
          RequireLowercase: true
          RequireNumbers: true
          RequireSymbols: false

  UserPoolClient:
    Type: AWS::Cognito::UserPoolClient
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      ClientName: !Sub "${ProjectName}-client-${Environment}"
      UserPoolId: !Ref UserPool
      GenerateSecret: true
      ExplicitAuthFlows:
        - ADMIN_NO_SRP_AUTH
        - ALLOW_USER_PASSWORD_AUTH
        - ALLOW_REFRESH_TOKEN_AUTH
      RefreshTokenValidity: 30
      AccessTokenValidity: 60
      IdTokenValidity: 60

  # Lambda Execution Role
  LambdaExecutionRole:
    Type: AWS::IAM::Role
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      RoleName: !Sub "${ProjectName}-lambda-execution-role-${Environment}"
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
      Policies:
        - PolicyName: CognitoAccess
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - cognito-idp:*
                Resource: !GetAtt UserPool.Arn
        - PolicyName: DynamoDBAccess
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:GetItem
                  - dynamodb:PutItem
                  - dynamodb:UpdateItem
                  - dynamodb:DeleteItem
                  - dynamodb:Query
                  - dynamodb:Scan
                Resource:
                  - !GetAtt UsersTable.Arn
                  - !Sub "${UsersTable.Arn}/index/*"
                  - !GetAtt PostsTable.Arn
                  - !GetAtt MediaTable.Arn
                  - !GetAtt UserProfilesTable.Arn
                  - !GetAtt CommentsTable.Arn
                  - !GetAtt LikesTable.Arn
                  - !GetAtt FollowsTable.Arn
        - PolicyName: S3Access
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:PutObject
                  - s3:DeleteObject
                  - s3:ListBucket
                Resource:
                  - !GetAtt MediaBucket.Arn
                  - !Sub "${MediaBucket.Arn}/*"
                  - !GetAtt AvatarsBucket.Arn
                  - !Sub "${AvatarsBucket.Arn}/*"
                  - !GetAtt TempBucket.Arn
                  - !Sub "${TempBucket.Arn}/*"
                  - !GetAtt LambdaCodeBucket.Arn
                  - !Sub "${LambdaCodeBucket.Arn}/*"

  # API Gateway
  ApiGateway:
    Type: AWS::ApiGateway::RestApi
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      Name: !Sub "${ProjectName}-api-${Environment}"
      Description: GameFlex API Gateway
      EndpointConfiguration:
        Types:
          - REGIONAL

  # Auth Resource
  AuthResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !GetAtt ApiGateway.RootResourceId
      PathPart: auth

  # Auth Signin Resource
  AuthSigninResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !Ref AuthResource
      PathPart: signin

  AuthSigninMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref AuthSigninResource
      HttpMethod: POST
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:${ProjectName}-auth-${Environment}/invocations"

  # Auth Signup Resource
  AuthSignupResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !Ref AuthResource
      PathPart: signup

  AuthSignupMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref AuthSignupResource
      HttpMethod: POST
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:${ProjectName}-auth-${Environment}/invocations"

  # Posts Resource
  PostsResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !GetAtt ApiGateway.RootResourceId
      PathPart: posts

  PostsGetMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref PostsResource
      HttpMethod: GET
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:${ProjectName}-posts-${Environment}/invocations"

  PostsPostMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref PostsResource
      HttpMethod: POST
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:${ProjectName}-posts-${Environment}/invocations"

  # Media Resource
  MediaResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !GetAtt ApiGateway.RootResourceId
      PathPart: media

  MediaUploadResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !Ref MediaResource
      PathPart: upload

  MediaUploadMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref MediaUploadResource
      HttpMethod: POST
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:${ProjectName}-media-${Environment}/invocations"

  # Users Resource
  UsersResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !GetAtt ApiGateway.RootResourceId
      PathPart: users

  UsersProfileResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !Ref UsersResource
      PathPart: profile

  UsersProfileGetMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref UsersProfileResource
      HttpMethod: GET
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:${ProjectName}-users-${Environment}/invocations"

  # Lambda Permissions for API Gateway
  AuthLambdaPermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Sub "${ProjectName}-auth-${Environment}"
      Action: lambda:InvokeFunction
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub "arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${ApiGateway}/*/*"

  PostsLambdaPermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Sub "${ProjectName}-posts-${Environment}"
      Action: lambda:InvokeFunction
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub "arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${ApiGateway}/*/*"

  MediaLambdaPermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Sub "${ProjectName}-media-${Environment}"
      Action: lambda:InvokeFunction
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub "arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${ApiGateway}/*/*"

  UsersLambdaPermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Sub "${ProjectName}-users-${Environment}"
      Action: lambda:InvokeFunction
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub "arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${ApiGateway}/*/*"

  # API Gateway Deployment
  ApiDeployment:
    Type: AWS::ApiGateway::Deployment
    DependsOn:
      - AuthSigninMethod
      - AuthSignupMethod
      - PostsGetMethod
      - PostsPostMethod
      - MediaUploadMethod
      - UsersProfileGetMethod
      - AuthLambdaPermission
      - PostsLambdaPermission
      - MediaLambdaPermission
      - UsersLambdaPermission
    Properties:
      RestApiId: !Ref ApiGateway
      StageName: !Ref Environment

Outputs:
  UserPoolId:
    Description: Cognito User Pool ID
    Value: !Ref UserPool
    Export:
      Name: !Sub "${ProjectName}-user-pool-id-${Environment}"

  UserPoolClientId:
    Description: Cognito User Pool Client ID
    Value: !Ref UserPoolClient
    Export:
      Name: !Sub "${ProjectName}-user-pool-client-id-${Environment}"

  ApiGatewayId:
    Description: API Gateway ID
    Value: !Ref ApiGateway
    Export:
      Name: !Sub "${ProjectName}-api-gateway-id-${Environment}"

  ApiGatewayUrl:
    Description: API Gateway URL
    Value: !If
      - IsDevelopment
      - !Sub "http://localhost:45660/restapis/${ApiGateway}/development/_user_request_"
      - !Sub "https://${ApiGateway}.execute-api.${AWS::Region}.amazonaws.com/${Environment}"
    Export:
      Name: !Sub "${ProjectName}-api-url-${Environment}"

  MediaBucketName:
    Description: S3 Media Bucket Name
    Value: !Ref MediaBucket
    Export:
      Name: !Sub "${ProjectName}-media-bucket-${Environment}"

  AvatarsBucketName:
    Description: S3 Avatars Bucket Name
    Value: !Ref AvatarsBucket
    Export:
      Name: !Sub "${ProjectName}-avatars-bucket-${Environment}"

  TempBucketName:
    Description: S3 Temp Bucket Name
    Value: !Ref TempBucket
    Export:
      Name: !Sub "${ProjectName}-temp-bucket-${Environment}"

  UsersTableName:
    Description: DynamoDB Users Table Name
    Value: !Ref UsersTable
    Export:
      Name: !Sub "${ProjectName}-users-table-${Environment}"

  PostsTableName:
    Description: DynamoDB Posts Table Name
    Value: !Ref PostsTable
    Export:
      Name: !Sub "${ProjectName}-posts-table-${Environment}"

  MediaTableName:
    Description: DynamoDB Media Table Name
    Value: !Ref MediaTable
    Export:
      Name: !Sub "${ProjectName}-media-table-${Environment}"

  UserProfilesTableName:
    Description: DynamoDB UserProfiles Table Name
    Value: !Ref UserProfilesTable
    Export:
      Name: !Sub "${ProjectName}-user-profiles-table-${Environment}"

  CommentsTableName:
    Description: DynamoDB Comments Table Name
    Value: !Ref CommentsTable
    Export:
      Name: !Sub "${ProjectName}-comments-table-${Environment}"

  LikesTableName:
    Description: DynamoDB Likes Table Name
    Value: !Ref LikesTable
    Export:
      Name: !Sub "${ProjectName}-likes-table-${Environment}"

  FollowsTableName:
    Description: DynamoDB Follows Table Name
    Value: !Ref FollowsTable
    Export:
      Name: !Sub "${ProjectName}-follows-table-${Environment}"

  LambdaCodeBucketName:
    Description: S3 Lambda Code Bucket Name
    Value: !Ref LambdaCodeBucket
    Export:
      Name: !Sub "${ProjectName}-lambda-code-bucket-${Environment}"

  LambdaExecutionRoleArn:
    Description: Lambda Execution Role ARN
    Value: !GetAtt LambdaExecutionRole.Arn
    Export:
      Name: !Sub "${ProjectName}-lambda-execution-role-arn-${Environment}"
