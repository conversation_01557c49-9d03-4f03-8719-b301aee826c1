AWSTemplateFormatVersion: "2010-09-09"
Description: "GameFlex AWS Infrastructure - Production Ready Template"

Parameters:
  Environment:
    Type: String
    Default: production
    AllowedValues:
      - development
      - qa
      - staging
      - production
    Description: Environment name

  ProjectName:
    Type: String
    Default: gameflex
    Description: Project name for resource naming

  S3BucketVersioning:
    Type: String
    Default: Enabled
    AllowedValues: [Enabled, Suspended]
    Description: S3 bucket versioning configuration

  S3BucketEncryption:
    Type: String
    Default: "true"
    AllowedValues: ["true", "false"]
    Description: Enable S3 bucket encryption

  EnableS3AccessLogging:
    Type: String
    Default: "true"
    AllowedValues: ["true", "false"]
    Description: Enable S3 access logging

  CognitoPasswordMinLength:
    Type: Number
    Default: 8
    MinValue: 6
    MaxValue: 99
    Description: Minimum password length for Cognito

  CognitoPasswordRequireUppercase:
    Type: String
    Default: "true"
    AllowedValues: ["true", "false"]
    Description: Require uppercase letters in passwords

  CognitoPasswordRequireLowercase:
    Type: String
    Default: "true"
    AllowedValues: ["true", "false"]
    Description: Require lowercase letters in passwords

  CognitoPasswordRequireNumbers:
    Type: String
    Default: "true"
    AllowedValues: ["true", "false"]
    Description: Require numbers in passwords

  CognitoPasswordRequireSymbols:
    Type: String
    Default: "true"
    AllowedValues: ["true", "false"]
    Description: Require symbols in passwords

  CognitoMfaConfiguration:
    Type: String
    Default: OPTIONAL
    AllowedValues: [OFF, ON, OPTIONAL]
    Description: MFA configuration for Cognito

  VPCCidr:
    Type: String
    Default: 10.0.0.0/16
    Description: CIDR block for VPC

  PublicSubnet1Cidr:
    Type: String
    Default: ********/24
    Description: CIDR block for public subnet 1

  PublicSubnet2Cidr:
    Type: String
    Default: ********/24
    Description: CIDR block for public subnet 2

  PrivateSubnet1Cidr:
    Type: String
    Default: ********/24
    Description: CIDR block for private subnet 1

  PrivateSubnet2Cidr:
    Type: String
    Default: ********/24
    Description: CIDR block for private subnet 2

Conditions:
  IsProduction: !Equals [!Ref Environment, production]
  IsNotDevelopment: !Not [!Equals [!Ref Environment, development]]
  EnableS3EncryptionCondition: !Equals [!Ref S3BucketEncryption, "true"]
  EnableS3AccessLoggingCondition: !Equals [!Ref EnableS3AccessLogging, "true"]
  CognitoMfaEnabled: !Not [!Equals [!Ref CognitoMfaConfiguration, "OFF"]]

Resources:
  # VPC and Networking
  VPC:
    Type: AWS::EC2::VPC
    Properties:
      CidrBlock: !Ref VPCCidr
      EnableDnsHostnames: true
      EnableDnsSupport: true
      Tags:
        - Key: Name
          Value: !Sub "${ProjectName}-vpc-${Environment}"
        - Key: Environment
          Value: !Ref Environment
        - Key: Project
          Value: !Ref ProjectName

  InternetGateway:
    Type: AWS::EC2::InternetGateway
    Properties:
      Tags:
        - Key: Name
          Value: !Sub "${ProjectName}-igw-${Environment}"
        - Key: Environment
          Value: !Ref Environment

  InternetGatewayAttachment:
    Type: AWS::EC2::VPCGatewayAttachment
    Properties:
      InternetGatewayId: !Ref InternetGateway
      VpcId: !Ref VPC

  PublicSubnet1:
    Type: AWS::EC2::Subnet
    Properties:
      VpcId: !Ref VPC
      AvailabilityZone: !Select [0, !GetAZs ""]
      CidrBlock: !Ref PublicSubnet1Cidr
      MapPublicIpOnLaunch: true
      Tags:
        - Key: Name
          Value: !Sub "${ProjectName}-public-subnet-1-${Environment}"
        - Key: Environment
          Value: !Ref Environment

  PublicSubnet2:
    Type: AWS::EC2::Subnet
    Properties:
      VpcId: !Ref VPC
      AvailabilityZone: !Select [1, !GetAZs ""]
      CidrBlock: !Ref PublicSubnet2Cidr
      MapPublicIpOnLaunch: true
      Tags:
        - Key: Name
          Value: !Sub "${ProjectName}-public-subnet-2-${Environment}"
        - Key: Environment
          Value: !Ref Environment

  PrivateSubnet1:
    Type: AWS::EC2::Subnet
    Properties:
      VpcId: !Ref VPC
      AvailabilityZone: !Select [0, !GetAZs ""]
      CidrBlock: !Ref PrivateSubnet1Cidr
      Tags:
        - Key: Name
          Value: !Sub "${ProjectName}-private-subnet-1-${Environment}"
        - Key: Environment
          Value: !Ref Environment

  PrivateSubnet2:
    Type: AWS::EC2::Subnet
    Properties:
      VpcId: !Ref VPC
      AvailabilityZone: !Select [1, !GetAZs ""]
      CidrBlock: !Ref PrivateSubnet2Cidr
      Tags:
        - Key: Name
          Value: !Sub "${ProjectName}-private-subnet-2-${Environment}"
        - Key: Environment
          Value: !Ref Environment

  # NAT Gateways for private subnets
  NatGateway1EIP:
    Type: AWS::EC2::EIP
    DependsOn: InternetGatewayAttachment
    Condition: IsNotDevelopment
    Properties:
      Domain: vpc
      Tags:
        - Key: Name
          Value: !Sub "${ProjectName}-nat-eip-1-${Environment}"

  NatGateway2EIP:
    Type: AWS::EC2::EIP
    DependsOn: InternetGatewayAttachment
    Condition: IsNotDevelopment
    Properties:
      Domain: vpc
      Tags:
        - Key: Name
          Value: !Sub "${ProjectName}-nat-eip-2-${Environment}"

  NatGateway1:
    Type: AWS::EC2::NatGateway
    Condition: IsNotDevelopment
    Properties:
      AllocationId: !GetAtt NatGateway1EIP.AllocationId
      SubnetId: !Ref PublicSubnet1
      Tags:
        - Key: Name
          Value: !Sub "${ProjectName}-nat-1-${Environment}"

  NatGateway2:
    Type: AWS::EC2::NatGateway
    Condition: IsNotDevelopment
    Properties:
      AllocationId: !GetAtt NatGateway2EIP.AllocationId
      SubnetId: !Ref PublicSubnet2
      Tags:
        - Key: Name
          Value: !Sub "${ProjectName}-nat-2-${Environment}"

  # Route Tables
  PublicRouteTable:
    Type: AWS::EC2::RouteTable
    Properties:
      VpcId: !Ref VPC
      Tags:
        - Key: Name
          Value: !Sub "${ProjectName}-public-routes-${Environment}"
        - Key: Environment
          Value: !Ref Environment

  DefaultPublicRoute:
    Type: AWS::EC2::Route
    DependsOn: InternetGatewayAttachment
    Properties:
      RouteTableId: !Ref PublicRouteTable
      DestinationCidrBlock: 0.0.0.0/0
      GatewayId: !Ref InternetGateway

  PublicSubnet1RouteTableAssociation:
    Type: AWS::EC2::SubnetRouteTableAssociation
    Properties:
      RouteTableId: !Ref PublicRouteTable
      SubnetId: !Ref PublicSubnet1

  PublicSubnet2RouteTableAssociation:
    Type: AWS::EC2::SubnetRouteTableAssociation
    Properties:
      RouteTableId: !Ref PublicRouteTable
      SubnetId: !Ref PublicSubnet2

  PrivateRouteTable1:
    Type: AWS::EC2::RouteTable
    Properties:
      VpcId: !Ref VPC
      Tags:
        - Key: Name
          Value: !Sub "${ProjectName}-private-routes-1-${Environment}"
        - Key: Environment
          Value: !Ref Environment

  DefaultPrivateRoute1:
    Type: AWS::EC2::Route
    Condition: IsNotDevelopment
    Properties:
      RouteTableId: !Ref PrivateRouteTable1
      DestinationCidrBlock: 0.0.0.0/0
      NatGatewayId: !Ref NatGateway1

  PrivateSubnet1RouteTableAssociation:
    Type: AWS::EC2::SubnetRouteTableAssociation
    Properties:
      RouteTableId: !Ref PrivateRouteTable1
      SubnetId: !Ref PrivateSubnet1

  PrivateRouteTable2:
    Type: AWS::EC2::RouteTable
    Properties:
      VpcId: !Ref VPC
      Tags:
        - Key: Name
          Value: !Sub "${ProjectName}-private-routes-2-${Environment}"
        - Key: Environment
          Value: !Ref Environment

  DefaultPrivateRoute2:
    Type: AWS::EC2::Route
    Condition: IsNotDevelopment
    Properties:
      RouteTableId: !Ref PrivateRouteTable2
      DestinationCidrBlock: 0.0.0.0/0
      NatGatewayId: !Ref NatGateway2

  PrivateSubnet2RouteTableAssociation:
    Type: AWS::EC2::SubnetRouteTableAssociation
    Properties:
      RouteTableId: !Ref PrivateRouteTable2
      SubnetId: !Ref PrivateSubnet2

  # Security Groups
  LambdaSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupName: !Sub "${ProjectName}-lambda-sg-${Environment}"
      GroupDescription: Security group for Lambda functions
      VpcId: !Ref VPC
      SecurityGroupEgress:
        - IpProtocol: -1
          CidrIp: 0.0.0.0/0
      Tags:
        - Key: Name
          Value: !Sub "${ProjectName}-lambda-sg-${Environment}"
        - Key: Environment
          Value: !Ref Environment

  # S3 Buckets
  S3AccessLogsBucket:
    Type: AWS::S3::Bucket
    Condition: EnableS3AccessLoggingCondition
    Properties:
      BucketName: !Sub "${ProjectName}-access-logs-${Environment}-${AWS::AccountId}"
      OwnershipControls:
        Rules:
          - ObjectOwnership: BucketOwnerPreferred
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true
      VersioningConfiguration:
        Status: !Ref S3BucketVersioning
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: AES256
            BucketKeyEnabled: true
      LifecycleConfiguration:
        Rules:
          - Id: DeleteOldAccessLogs
            Status: Enabled
            ExpirationInDays: 90
      Tags:
        - Key: Name
          Value: !Sub "${ProjectName}-access-logs-${Environment}"
        - Key: Environment
          Value: !Ref Environment

  MediaBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub "${ProjectName}-media-${Environment}"
      PublicAccessBlockConfiguration:
        BlockPublicAcls: false
        BlockPublicPolicy: false
        IgnorePublicAcls: false
        RestrictPublicBuckets: false
      VersioningConfiguration:
        Status: !Ref S3BucketVersioning
      BucketEncryption: !If
        - EnableS3EncryptionCondition
        - ServerSideEncryptionConfiguration:
            - ServerSideEncryptionByDefault:
                SSEAlgorithm: AES256
              BucketKeyEnabled: true
        - !Ref AWS::NoValue
      LoggingConfiguration: !If
        - EnableS3AccessLoggingCondition
        - DestinationBucketName: !Ref S3AccessLogsBucket
          LogFilePrefix: media-access-logs/
        - !Ref AWS::NoValue
      CorsConfiguration:
        CorsRules:
          - AllowedHeaders:
              - "*"
            AllowedMethods:
              - GET
              - PUT
              - POST
              - DELETE
              - HEAD
            AllowedOrigins:
              - "*"
            MaxAge: 3000
      Tags:
        - Key: Name
          Value: !Sub "${ProjectName}-media-${Environment}"
        - Key: Environment
          Value: !Ref Environment

  AvatarsBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub "${ProjectName}-avatars-${Environment}"
      PublicAccessBlockConfiguration:
        BlockPublicAcls: false
        BlockPublicPolicy: false
        IgnorePublicAcls: false
        RestrictPublicBuckets: false
      VersioningConfiguration:
        Status: !Ref S3BucketVersioning
      BucketEncryption: !If
        - EnableS3EncryptionCondition
        - ServerSideEncryptionConfiguration:
            - ServerSideEncryptionByDefault:
                SSEAlgorithm: AES256
              BucketKeyEnabled: true
        - !Ref AWS::NoValue
      LoggingConfiguration: !If
        - EnableS3AccessLoggingCondition
        - DestinationBucketName: !Ref S3AccessLogsBucket
          LogFilePrefix: avatars-access-logs/
        - !Ref AWS::NoValue
      CorsConfiguration:
        CorsRules:
          - AllowedHeaders:
              - "*"
            AllowedMethods:
              - GET
              - PUT
              - POST
              - DELETE
              - HEAD
            AllowedOrigins:
              - "*"
            MaxAge: 3000
      Tags:
        - Key: Name
          Value: !Sub "${ProjectName}-avatars-${Environment}"
        - Key: Environment
          Value: !Ref Environment

  TempBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub "${ProjectName}-temp-${Environment}"
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true
      BucketEncryption: !If
        - EnableS3EncryptionCondition
        - ServerSideEncryptionConfiguration:
            - ServerSideEncryptionByDefault:
                SSEAlgorithm: AES256
              BucketKeyEnabled: true
        - !Ref AWS::NoValue
      LifecycleConfiguration:
        Rules:
          - Id: DeleteTempFiles
            Status: Enabled
            ExpirationInDays: 1
      Tags:
        - Key: Name
          Value: !Sub "${ProjectName}-temp-${Environment}"
        - Key: Environment
          Value: !Ref Environment

  # DynamoDB Tables
  UsersTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      TableName: !Sub "${ProjectName}-${Environment}-Users"
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: !If [IsProduction, true, false]
      Tags:
        - Key: Name
          Value: !Sub "${ProjectName}-users-table-${Environment}"
        - Key: Environment
          Value: !Ref Environment

  UserProfilesTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      TableName: !Sub "${ProjectName}-${Environment}-UserProfiles"
      AttributeDefinitions:
        - AttributeName: user_id
          AttributeType: S
      KeySchema:
        - AttributeName: user_id
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: !If [IsProduction, true, false]
      Tags:
        - Key: Name
          Value: !Sub "${ProjectName}-user-profiles-table-${Environment}"
        - Key: Environment
          Value: !Ref Environment

  ChannelsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      TableName: !Sub "${ProjectName}-${Environment}-Channels"
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: !If [IsProduction, true, false]
      Tags:
        - Key: Name
          Value: !Sub "${ProjectName}-channels-table-${Environment}"
        - Key: Environment
          Value: !Ref Environment

  ChannelMembersTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      TableName: !Sub "${ProjectName}-${Environment}-ChannelMembers"
      AttributeDefinitions:
        - AttributeName: channel_id
          AttributeType: S
        - AttributeName: user_id
          AttributeType: S
      KeySchema:
        - AttributeName: channel_id
          KeyType: HASH
        - AttributeName: user_id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: !If [IsProduction, true, false]
      Tags:
        - Key: Name
          Value: !Sub "${ProjectName}-channel-members-table-${Environment}"
        - Key: Environment
          Value: !Ref Environment

  MediaTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      TableName: !Sub "${ProjectName}-${Environment}-Media"
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: !If [IsProduction, true, false]
      Tags:
        - Key: Name
          Value: !Sub "${ProjectName}-media-table-${Environment}"
        - Key: Environment
          Value: !Ref Environment

  PostsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      TableName: !Sub "${ProjectName}-${Environment}-Posts"
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: !If [IsProduction, true, false]
      Tags:
        - Key: Name
          Value: !Sub "${ProjectName}-posts-table-${Environment}"
        - Key: Environment
          Value: !Ref Environment

  CommentsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      TableName: !Sub "${ProjectName}-${Environment}-Comments"
      AttributeDefinitions:
        - AttributeName: post_id
          AttributeType: S
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: post_id
          KeyType: HASH
        - AttributeName: id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: !If [IsProduction, true, false]
      Tags:
        - Key: Name
          Value: !Sub "${ProjectName}-comments-table-${Environment}"
        - Key: Environment
          Value: !Ref Environment

  LikesTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      TableName: !Sub "${ProjectName}-${Environment}-Likes"
      AttributeDefinitions:
        - AttributeName: post_id
          AttributeType: S
        - AttributeName: user_id
          AttributeType: S
      KeySchema:
        - AttributeName: post_id
          KeyType: HASH
        - AttributeName: user_id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: !If [IsProduction, true, false]
      Tags:
        - Key: Name
          Value: !Sub "${ProjectName}-likes-table-${Environment}"
        - Key: Environment
          Value: !Ref Environment

  FollowsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      TableName: !Sub "${ProjectName}-${Environment}-Follows"
      AttributeDefinitions:
        - AttributeName: follower_id
          AttributeType: S
        - AttributeName: following_id
          AttributeType: S
      KeySchema:
        - AttributeName: follower_id
          KeyType: HASH
        - AttributeName: following_id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: !If [IsProduction, true, false]
      Tags:
        - Key: Name
          Value: !Sub "${ProjectName}-follows-table-${Environment}"
        - Key: Environment
          Value: !Ref Environment

  NotificationsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      TableName: !Sub "${ProjectName}-${Environment}-Notifications"
      AttributeDefinitions:
        - AttributeName: user_id
          AttributeType: S
        - AttributeName: created_at
          AttributeType: S
      KeySchema:
        - AttributeName: user_id
          KeyType: HASH
        - AttributeName: created_at
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: !If [IsProduction, true, false]
      Tags:
        - Key: Name
          Value: !Sub "${ProjectName}-notifications-table-${Environment}"
        - Key: Environment
          Value: !Ref Environment

  # Cognito User Pool
  UserPool:
    Type: AWS::Cognito::UserPool
    Properties:
      UserPoolName: !Sub "${ProjectName}-users-${Environment}"
      AutoVerifiedAttributes:
        - email
      UsernameAttributes:
        - email
      UsernameConfiguration:
        CaseSensitive: false
      Policies:
        PasswordPolicy:
          MinimumLength: !Ref CognitoPasswordMinLength
          RequireUppercase: !Ref CognitoPasswordRequireUppercase
          RequireLowercase: !Ref CognitoPasswordRequireLowercase
          RequireNumbers: !Ref CognitoPasswordRequireNumbers
          RequireSymbols: !Ref CognitoPasswordRequireSymbols
          TemporaryPasswordValidityDays: 7
      MfaConfiguration: !Ref CognitoMfaConfiguration
      EnabledMfas: !If
        - CognitoMfaEnabled
        - - SMS_MFA
          - SOFTWARE_TOKEN_MFA
        - !Ref AWS::NoValue
      SmsConfiguration: !If
        - CognitoMfaEnabled
        - SnsCallerArn: !GetAtt CognitoSMSRole.Arn
          ExternalId: !Sub "${ProjectName}-cognito-sms-${Environment}"
        - !Ref AWS::NoValue
      Schema:
        - Name: email
          AttributeDataType: String
          Required: true
          Mutable: true
        - Name: given_name
          AttributeDataType: String
          Required: false
          Mutable: true
        - Name: family_name
          AttributeDataType: String
          Required: false
          Mutable: true
        - Name: username
          AttributeDataType: String
          Required: false
          Mutable: true
      UserPoolTags:
        Name: !Sub "${ProjectName}-user-pool-${Environment}"
        Environment: !Ref Environment

  UserPoolClient:
    Type: AWS::Cognito::UserPoolClient
    Properties:
      ClientName: !Sub "${ProjectName}-client-${Environment}"
      UserPoolId: !Ref UserPool
      GenerateSecret: true
      ExplicitAuthFlows:
        - ADMIN_NO_SRP_AUTH
        - ALLOW_USER_PASSWORD_AUTH
        - ALLOW_REFRESH_TOKEN_AUTH
        - ALLOW_USER_SRP_AUTH
      RefreshTokenValidity: 30
      AccessTokenValidity: 60
      IdTokenValidity: 60
      TokenValidityUnits:
        RefreshToken: days
        AccessToken: minutes
        IdToken: minutes
      PreventUserExistenceErrors: ENABLED
      SupportedIdentityProviders:
        - COGNITO

  IdentityPool:
    Type: AWS::Cognito::IdentityPool
    Properties:
      IdentityPoolName: !Sub "${ProjectName}-identity-${Environment}"
      AllowUnauthenticatedIdentities: false
      CognitoIdentityProviders:
        - ClientId: !Ref UserPoolClient
          ProviderName: !GetAtt UserPool.ProviderName
          ServerSideTokenCheck: false

  CognitoSMSRole:
    Type: AWS::IAM::Role
    Condition: CognitoMfaEnabled
    Properties:
      RoleName: !Sub "${ProjectName}-cognito-sms-role-${Environment}"
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: cognito-idp.amazonaws.com
            Action: sts:AssumeRole
            Condition:
              StringEquals:
                "sts:ExternalId": !Sub "${ProjectName}-cognito-sms-${Environment}"
      Policies:
        - PolicyName: CognitoSMSPolicy
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - sns:Publish
                Resource: "*"
      Tags:
        - Key: Name
          Value: !Sub "${ProjectName}-cognito-sms-role-${Environment}"
        - Key: Environment
          Value: !Ref Environment

  # IAM Roles for Cognito Identity Pool
  CognitoAuthenticatedRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub "${ProjectName}-authenticated-role-${Environment}"
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Federated: cognito-identity.amazonaws.com
            Action: sts:AssumeRoleWithWebIdentity
            Condition:
              StringEquals:
                "cognito-identity.amazonaws.com:aud": !Ref IdentityPool
              "ForAnyValue:StringLike":
                "cognito-identity.amazonaws.com:amr": authenticated
      Policies:
        - PolicyName: CognitoAuthenticatedPolicy
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:PutObject
                  - s3:DeleteObject
                Resource:
                  - !Sub "${MediaBucket}/*"
                  - !Sub "${AvatarsBucket}/*"
                  - !Sub "${TempBucket}/*"
              - Effect: Allow
                Action:
                  - s3:ListBucket
                Resource:
                  - !GetAtt MediaBucket.Arn
                  - !GetAtt AvatarsBucket.Arn
                  - !GetAtt TempBucket.Arn
      Tags:
        - Key: Name
          Value: !Sub "${ProjectName}-authenticated-role-${Environment}"
        - Key: Environment
          Value: !Ref Environment

  IdentityPoolRoleAttachment:
    Type: AWS::Cognito::IdentityPoolRoleAttachment
    Properties:
      IdentityPoolId: !Ref IdentityPool
      Roles:
        authenticated: !GetAtt CognitoAuthenticatedRole.Arn

  # Lambda Execution Role
  LambdaExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub "${ProjectName}-lambda-execution-role-${Environment}"
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole
      Policies:
        - PolicyName: CognitoAccess
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - cognito-idp:*
                Resource: !GetAtt UserPool.Arn
        - PolicyName: S3Access
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:PutObject
                  - s3:DeleteObject
                  - s3:ListBucket
                Resource:
                  - !GetAtt MediaBucket.Arn
                  - !Sub "${MediaBucket}/*"
                  - !GetAtt AvatarsBucket.Arn
                  - !Sub "${AvatarsBucket}/*"
                  - !GetAtt TempBucket.Arn
                  - !Sub "${TempBucket}/*"
        - PolicyName: DynamoDBAccess
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:GetItem
                  - dynamodb:PutItem
                  - dynamodb:UpdateItem
                  - dynamodb:DeleteItem
                  - dynamodb:Query
                  - dynamodb:Scan
                  - dynamodb:BatchGetItem
                  - dynamodb:BatchWriteItem
                Resource:
                  - !GetAtt UsersTable.Arn
                  - !GetAtt UserProfilesTable.Arn
                  - !GetAtt ChannelsTable.Arn
                  - !GetAtt ChannelMembersTable.Arn
                  - !GetAtt MediaTable.Arn
                  - !GetAtt PostsTable.Arn
                  - !GetAtt CommentsTable.Arn
                  - !GetAtt LikesTable.Arn
                  - !GetAtt FollowsTable.Arn
                  - !GetAtt NotificationsTable.Arn
      Tags:
        - Key: Name
          Value: !Sub "${ProjectName}-lambda-execution-role-${Environment}"
        - Key: Environment
          Value: !Ref Environment

Outputs:
  VPCId:
    Description: VPC ID
    Value: !Ref VPC
    Export:
      Name: !Sub "${ProjectName}-vpc-id-${Environment}"

  PrivateSubnet1Id:
    Description: Private Subnet 1 ID
    Value: !Ref PrivateSubnet1
    Export:
      Name: !Sub "${ProjectName}-private-subnet-1-id-${Environment}"

  PrivateSubnet2Id:
    Description: Private Subnet 2 ID
    Value: !Ref PrivateSubnet2
    Export:
      Name: !Sub "${ProjectName}-private-subnet-2-id-${Environment}"

  UserPoolId:
    Description: Cognito User Pool ID
    Value: !Ref UserPool
    Export:
      Name: !Sub "${ProjectName}-user-pool-id-${Environment}"

  UserPoolClientId:
    Description: Cognito User Pool Client ID
    Value: !Ref UserPoolClient
    Export:
      Name: !Sub "${ProjectName}-user-pool-client-id-${Environment}"

  IdentityPoolId:
    Description: Cognito Identity Pool ID
    Value: !Ref IdentityPool
    Export:
      Name: !Sub "${ProjectName}-identity-pool-id-${Environment}"

  MediaBucketName:
    Description: S3 Media Bucket Name
    Value: !Ref MediaBucket
    Export:
      Name: !Sub "${ProjectName}-media-bucket-${Environment}"

  AvatarsBucketName:
    Description: S3 Avatars Bucket Name
    Value: !Ref AvatarsBucket
    Export:
      Name: !Sub "${ProjectName}-avatars-bucket-${Environment}"

  TempBucketName:
    Description: S3 Temp Bucket Name
    Value: !Ref TempBucket
    Export:
      Name: !Sub "${ProjectName}-temp-bucket-${Environment}"

  LambdaExecutionRoleArn:
    Description: Lambda Execution Role ARN
    Value: !GetAtt LambdaExecutionRole.Arn
    Export:
      Name: !Sub "${ProjectName}-lambda-execution-role-arn-${Environment}"

  LambdaSecurityGroupId:
    Description: Lambda Security Group ID
    Value: !Ref LambdaSecurityGroup
    Export:
      Name: !Sub "${ProjectName}-lambda-sg-id-${Environment}"
