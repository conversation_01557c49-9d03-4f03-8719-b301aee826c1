{"name": "gameflex-posts-lambda", "version": "1.0.0", "description": "GameFlex Posts Lambda Functions", "main": "dist/index.js", "scripts": {"build": "esbuild src/index.ts --bundle --minify --sourcemap --platform=node --target=es2020 --outfile=dist/index.js --watch", "build-once": "esbuild src/index.ts --bundle --minify --sourcemap --platform=node --target=es2020 --outfile=dist/index.js", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean"}, "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.450.0", "@aws-sdk/client-dynamodb": "^3.450.0", "@aws-sdk/client-s3": "^3.450.0", "@aws-sdk/lib-dynamodb": "^3.450.0", "uuid": "^9.0.1"}, "devDependencies": {"@types/aws-lambda": "^8.10.131", "@types/node": "^20.10.0", "@types/uuid": "^9.0.7", "esbuild": "^0.19.0", "rimraf": "^5.0.5", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0"}}