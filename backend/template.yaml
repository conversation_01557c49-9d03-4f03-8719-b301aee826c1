AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: GameFlex Backend - SAM Template for LocalStack Development

Parameters:
  Environment:
    Type: String
    Default: development
    AllowedValues:
      - development
      - staging
      - production
    Description: Environment name

  ProjectName:
    Type: String
    Default: gameflex
    Description: Project name for resource naming

Globals:
  Function:
    Runtime: nodejs20.x
    Timeout: 30
    MemorySize: 256
    Environment:
      Variables:
        ENVIRONMENT: !Ref Environment
        PROJECT_NAME: !Ref ProjectName
        USER_POOL_ID: !Ref UserPool
        USER_POOL_CLIENT_ID: !Ref UserPoolClient
        POSTS_TABLE: !Ref PostsTable
        MEDIA_TABLE: !Ref MediaTable
        USER_PROFILES_TABLE: !Ref UserProfilesTable
        COMMENTS_TABLE: !Ref CommentsTable
        LIKES_TABLE: !Ref LikesTable
        FOLLOWS_TABLE: !Ref FollowsTable
        # Note: S3 buckets removed - using CloudFlare R2 instead
        USERS_TABLE: !Ref UsersTable

Resources:
  # Note: S3 buckets removed - using CloudFlare R2 instead

  PostsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub ${ProjectName}-${Environment}-Posts
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST

  MediaTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub ${ProjectName}-${Environment}-Media
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST

  UserProfilesTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub ${ProjectName}-${Environment}-UserProfiles
      AttributeDefinitions:
        - AttributeName: user_id
          AttributeType: S
      KeySchema:
        - AttributeName: user_id
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST

  CommentsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub ${ProjectName}-${Environment}-Comments
      AttributeDefinitions:
        - AttributeName: post_id
          AttributeType: S
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: post_id
          KeyType: HASH
        - AttributeName: id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST

  LikesTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub ${ProjectName}-${Environment}-Likes
      AttributeDefinitions:
        - AttributeName: post_id
          AttributeType: S
        - AttributeName: user_id
          AttributeType: S
      KeySchema:
        - AttributeName: post_id
          KeyType: HASH
        - AttributeName: user_id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST

  FollowsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub ${ProjectName}-${Environment}-Follows
      AttributeDefinitions:
        - AttributeName: follower_id
          AttributeType: S
        - AttributeName: following_id
          AttributeType: S
      KeySchema:
        - AttributeName: follower_id
          KeyType: HASH
        - AttributeName: following_id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST

  # Cognito User Pool
  UserPool:
    Type: AWS::Cognito::UserPool
    Properties:
      UserPoolName: !Sub ${ProjectName}-users-${Environment}
      AutoVerifiedAttributes:
        - email
      UsernameAttributes:
        - email
      Policies:
        PasswordPolicy:
          MinimumLength: 8
          RequireUppercase: true
          RequireLowercase: true
          RequireNumbers: true
          RequireSymbols: false

  UserPoolClient:
    Type: AWS::Cognito::UserPoolClient
    Properties:
      ClientName: !Sub ${ProjectName}-client-${Environment}
      UserPoolId: !Ref UserPool
      GenerateSecret: false
      ExplicitAuthFlows:
        - ALLOW_ADMIN_USER_PASSWORD_AUTH
        - ALLOW_USER_PASSWORD_AUTH
        - ALLOW_REFRESH_TOKEN_AUTH
      TokenValidityUnits:
        RefreshToken: days
        AccessToken: minutes
        IdToken: minutes
      RefreshTokenValidity: 30
      AccessTokenValidity: 60
      IdTokenValidity: 60

  # Lambda Functions
  AuthFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${ProjectName}-auth-${Environment}
      CodeUri: src/auth/
      Handler: index.handler
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref UsersTable
        - Statement:
            - Effect: Allow
              Action:
                - cognito-idp:*
              Resource: !GetAtt UserPool.Arn
        - DynamoDBCrudPolicy:
            TableName: !Ref UsersTable
        - Statement:
            - Effect: Allow
              Action:
                - cognito-idp:Admin*
                - cognito-idp:DescribeIdentityProvider
                - cognito-idp:DescribeResourceServer
                - cognito-idp:DescribeUserPool
                - cognito-idp:DescribeUserPoolClient
                - cognito-idp:DescribeUserPoolDomain
                - cognito-idp:GetGroup
                - cognito-idp:ListGroups
                - cognito-idp:ListUserPoolClients
                - cognito-idp:ListUsers
                - cognito-idp:ListUsersInGroup
                - cognito-idp:UpdateGroup
              Resource: !GetAtt UserPool.Arn
      Events:
        AuthSignin:
          Type: Api
          Properties:
            Path: /auth/signin
            Method: POST
        AuthSignup:
          Type: Api
          Properties:
            Path: /auth/signup
            Method: POST
        AuthRefresh:
          Type: Api
          Properties:
            Path: /auth/refresh
            Method: POST
        AuthValidate:
          Type: Api
          Properties:
            Path: /auth/validate
            Method: GET
      Environment:
        Variables:
          USERPOOLCLIENT_USER_POOL_CLIENT_ID: !Ref UserPoolClient
          USERPOOL_USER_POOL_ID: !Ref UserPool
          USERPOOL_USER_POOL_ARN: !GetAtt UserPool.Arn
          USERSTABLE_TABLE_NAME: !Ref UsersTable
          USERSTABLE_TABLE_ARN: !GetAtt UsersTable.Arn

  PostsFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${ProjectName}-posts-${Environment}
      CodeUri: src/posts/
      Handler: index.handler
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref PostsTable
        - DynamoDBCrudPolicy:
            TableName: !Ref MediaTable
        - DynamoDBCrudPolicy:
            TableName: !Ref CommentsTable
        - DynamoDBCrudPolicy:
            TableName: !Ref LikesTable
        - DynamoDBCrudPolicy:
            TableName: !Ref UsersTable
        # Note: S3 policies removed - using CloudFlare R2 instead
      Events:
        GetPosts:
          Type: Api
          Properties:
            Path: /posts
            Method: GET
        CreatePost:
          Type: Api
          Properties:
            Path: /posts
            Method: POST
        GetPost:
          Type: Api
          Properties:
            Path: /posts/{id}
            Method: GET
        UpdatePost:
          Type: Api
          Properties:
            Path: /posts/{id}
            Method: PUT
        DeletePost:
          Type: Api
          Properties:
            Path: /posts/{id}
            Method: DELETE
        LikePost:
          Type: Api
          Properties:
            Path: /posts/{id}/like
            Method: POST
        UnlikePost:
          Type: Api
          Properties:
            Path: /posts/{id}/like
            Method: DELETE

  MediaFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${ProjectName}-media-${Environment}
      CodeUri: src/media/
      Handler: index.handler
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref MediaTable
        # Note: S3 policies removed - using CloudFlare R2 instead
      Events:
        UploadMedia:
          Type: Api
          Properties:
            Path: /media/upload
            Method: POST
        GetMedia:
          Type: Api
          Properties:
            Path: /media/{id}
            Method: GET
        DeleteMedia:
          Type: Api
          Properties:
            Path: /media/{id}
            Method: DELETE

  UsersFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${ProjectName}-users-${Environment}
      CodeUri: src/users/
      Handler: index.handler
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref UsersTable
        - DynamoDBCrudPolicy:
            TableName: !Ref UserProfilesTable
        - DynamoDBCrudPolicy:
            TableName: !Ref FollowsTable
        # Note: S3 policies removed - using CloudFlare R2 instead
      Events:
        GetProfile:
          Type: Api
          Properties:
            Path: /users/profile
            Method: GET
        UpdateProfile:
          Type: Api
          Properties:
            Path: /users/profile
            Method: PUT
        GetUser:
          Type: Api
          Properties:
            Path: /users/{id}
            Method: GET
        FollowUser:
          Type: Api
          Properties:
            Path: /users/{id}/follow
            Method: POST
        UnfollowUser:
          Type: Api
          Properties:
            Path: /users/{id}/follow
            Method: DELETE

  HealthFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${ProjectName}-health-${Environment}
      CodeUri: src/health/
      Handler: index.handler
      Events:
        HealthCheck:
          Type: Api
          Properties:
            Path: /health
            Method: GET
      Environment:
        Variables:
          USERS_TABLE_NAME: !Ref UsersTable
          USERS_TABLE_ARN: !GetAtt UsersTable.Arn
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref UsersTable

  # DynamoDB Tables
  UsersTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub ${ProjectName}-${Environment}-Users
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: email
          AttributeType: S
        - AttributeName: username
          AttributeType: S
        - AttributeName: cognito_user_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: EmailIndex
          KeySchema:
            - AttributeName: email
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: UsernameIndex
          KeySchema:
            - AttributeName: username
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: CognitoUserIdIndex
          KeySchema:
            - AttributeName: cognito_user_id
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      BillingMode: PAY_PER_REQUEST

Outputs:
  ApiGatewayUrl:
    Description: API Gateway endpoint URL
    Value: !Sub https://${ServerlessRestApi}.execute-api.${AWS::Region}.amazonaws.com/Prod/
    Export:
      Name: !Sub ${ProjectName}-api-url-${Environment}

  UserPoolId:
    Description: Cognito User Pool ID
    Value: !Ref UserPool
    Export:
      Name: !Sub ${ProjectName}-user-pool-id-${Environment}

  UserPoolClientId:
    Description: Cognito User Pool Client ID
    Value: !Ref UserPoolClient
    Export:
      Name: !Sub ${ProjectName}-user-pool-client-id-${Environment}

  # Note: S3 bucket outputs removed - using CloudFlare R2 instead

  UsersTableName:
    Description: DynamoDB Users Table Name
    Value: !Ref UsersTable
    Export:
      Name: !Sub ${ProjectName}-users-table-${Environment}

  PostsTableName:
    Description: DynamoDB Posts Table Name
    Value: !Ref PostsTable
    Export:
      Name: !Sub ${ProjectName}-posts-table-${Environment}

  MediaTableName:
    Description: DynamoDB Media Table Name
    Value: !Ref MediaTable
    Export:
      Name: !Sub ${ProjectName}-media-table-${Environment}

  UserProfilesTableName:
    Description: DynamoDB UserProfiles Table Name
    Value: !Ref UserProfilesTable
    Export:
      Name: !Sub ${ProjectName}-user-profiles-table-${Environment}

  CommentsTableName:
    Description: DynamoDB Comments Table Name
    Value: !Ref CommentsTable
    Export:
      Name: !Sub ${ProjectName}-comments-table-${Environment}

  LikesTableName:
    Description: DynamoDB Likes Table Name
    Value: !Ref LikesTable
    Export:
      Name: !Sub ${ProjectName}-likes-table-${Environment}

  FollowsTableName:
    Description: DynamoDB Follows Table Name
    Value: !Ref FollowsTable
    Export:
      Name: !Sub ${ProjectName}-follows-table-${Environment}
