# GameFlex SAM Backend Testing Guide

This guide explains how to test the GameFlex SAM backend with real integration tests.

## Prerequisites

1. **Backend must be running**: The integration tests make real HTTP requests to the backend
2. **Node.js and npm**: For running the tests
3. **AWS SAM CLI**: For running the backend locally

## Quick Start

### 1. Start the Backend

In one terminal, start the backend:

```bash
cd backend
npm start
```

This will start the SAM local API on `http://localhost:3000`

### 2. Run Integration Tests

In another terminal, run the integration tests:

```bash
cd backend
npm run test:integration
```

Or use the integration test script:

```bash
./scripts/run-integration-tests.sh
```

## Test Structure

### Integration Tests (`tests/integration/`)
- **Purpose**: Test the actual running backend
- **Scope**: Real HTTP requests to `http://localhost:3000`
- **Coverage**: End-to-end API workflows

### Test Files
- `api.test.js` - Main API integration tests
  - Health check
  - Authentication flow (signup, signin, refresh)
  - User profile management
  - Posts CRUD operations
  - Media upload/management

## Test Flow

The integration tests follow this sequence:

1. **Health Check**: Verify backend is running
2. **User Signup**: Create a new test user
3. **User Signin**: Authenticate and get tokens
4. **Token Refresh**: Test token refresh functionality
5. **Profile Management**: Get and update user profile
6. **Posts**: Create, read, update, delete posts
7. **Media**: Upload and manage media files

## Running Specific Tests

```bash
# Run all integration tests
npm run test:integration

# Run specific test file
npx jest tests/integration/api.test.js

# Run with verbose output
npx jest tests/integration --verbose

# Run in watch mode
npx jest tests/integration --watch
```

## Test Data

- Tests create unique test users with random IDs
- Test data is cleaned up automatically
- No persistent test data is left behind

## Troubleshooting

### Backend Not Running
```
⚠️ Backend is not running at http://localhost:3000
Please start the backend with: npm start
```

**Solution**: Start the backend in another terminal with `npm start`

### Port Already in Use
```
Error: listen EADDRINUSE: address already in use :::3000
```

**Solution**: Stop any existing processes on port 3000 or use a different port

### AWS Services Not Available
If you see errors related to DynamoDB, Cognito, or S3:

**Solution**: Make sure your AWS configuration is set up correctly or that you're using LocalStack if configured

## Test Configuration

### Environment Variables
Tests use these environment variables (set automatically):
- `NODE_ENV=test`
- `ENVIRONMENT=test`
- `PROJECT_NAME=gameflex`

### API Base URL
Tests connect to: `http://localhost:3000`

To test against a different URL, modify the `API_URL` constant in the test files.

## Writing New Tests

### Basic Test Structure
```javascript
describe('New Feature Tests', () => {
  it('should test new functionality', async () => {
    const response = await axios.get(`${API_URL}/new-endpoint`);
    
    expect(response.status).toBe(200);
    expect(response.data).toBeDefined();
  });
});
```

### Authentication Required Tests
```javascript
it('should access protected endpoint', async () => {
  const response = await axios.get(`${API_URL}/protected`, {
    headers: {
      Authorization: `Bearer ${authTokens.accessToken}`
    }
  });
  
  expect(response.status).toBe(200);
});
```

## Best Practices

1. **Start Fresh**: Always start with a clean backend instance
2. **Unique Data**: Use UUIDs for test data to avoid conflicts
3. **Error Handling**: Check both success and error responses
4. **Cleanup**: Tests should clean up after themselves
5. **Isolation**: Each test should be independent

## CI/CD Integration

For automated testing in CI/CD:

1. Start the backend: `npm start &`
2. Wait for health check: `curl --retry 10 http://localhost:3000/health`
3. Run tests: `npm run test:integration`
4. Stop backend: `npm run stop`

Example GitHub Actions workflow:
```yaml
- name: Start Backend
  run: npm start &
  working-directory: backend

- name: Wait for Backend
  run: |
    timeout 60 bash -c 'until curl -f http://localhost:3000/health; do sleep 2; done'

- name: Run Integration Tests
  run: npm run test:integration
  working-directory: backend
```
