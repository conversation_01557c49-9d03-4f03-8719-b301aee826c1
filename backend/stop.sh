#!/bin/bash

# GameFlex SAM Backend Stop Script
# This script stops the AWS SAM local environment and LocalStack

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[GAMEFLEX]${NC} $1"
}

# Stop SAM local process
stop_sam_local() {
    print_status "Stopping SAM local API..."
    
    if [ -f .sam_pid ]; then
        local pid=$(cat .sam_pid)
        if kill -0 $pid 2>/dev/null; then
            kill $pid
            print_status "SAM local API stopped"
        else
            print_warning "SAM local API process not found"
        fi
        rm .sam_pid
    else
        print_warning "SAM local API PID file not found"
    fi
    
    # Also try to kill any remaining sam processes
    pkill -f "sam local" 2>/dev/null || true
}

# Stop any remaining SAM processes
stop_sam_processes() {
    print_status "Stopping any remaining SAM processes..."

    # Kill any remaining sam processes
    pkill -f "sam local" 2>/dev/null || true

    print_status "SAM processes stopped"
}

# Clean up temporary files
cleanup_files() {
    print_status "Cleaning up temporary files..."
    
    # Remove any temporary files
    rm -f .sam_pid
    
    print_status "Cleanup completed"
}

# Main execution
main() {
    print_header "Stopping GameFlex SAM Backend"
    echo
    
    stop_sam_local
    stop_sam_processes
    cleanup_files
    
    echo
    print_status "GameFlex SAM Backend stopped successfully!"
}

# Run main function
main "$@"
