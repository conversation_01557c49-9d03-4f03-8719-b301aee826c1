# GameFlex SAM Backend

A serverless backend for GameFlex built with AWS SAM (Serverless Application Model) for local development and AWS deployment.

## Architecture

This backend provides a complete serverless architecture including:

- **Authentication**: AWS Cognito User Pool for user management
- **API Gateway**: RESTful API endpoints
- **Lambda Functions**: Serverless compute for business logic
- **DynamoDB**: NoSQL database for data storage
- **S3**: Object storage for media files

## Services

### Lambda Functions

1. **Auth Service** (`/auth`)
   - User registration and authentication
   - JWT token management
   - Password management

2. **Posts Service** (`/posts`)
   - Create, read, update, delete posts
   - Like/unlike functionality
   - Post management

3. **Media Service** (`/media`)
   - File upload with presigned URLs
   - Media metadata management
   - S3 integration

4. **Users Service** (`/users`)
   - User profile management
   - Follow/unfollow functionality
   - User data management

5. **Health Service** (`/health`)
   - System health checks
   - Service status monitoring

### Database Tables

- **Users**: User account information
- **UserProfiles**: Extended user profile data
- **Posts**: User posts and content
- **Comments**: Post comments
- **Likes**: <PERSON> likes tracking
- **Follows**: User follow relationships
- **Media**: Media file metadata

### S3 Buckets

- **Media Bucket**: User-generated content
- **Avatars Bucket**: User profile pictures
- **Temp Bucket**: Temporary file storage

## Prerequisites

- **Docker**: For running Lambda functions locally
- **Node.js**: Version 18 or higher
- **AWS SAM CLI**: For local development and deployment

### Installation

1. **Install Docker**
   ```bash
   # Follow Docker installation guide for your OS
   # https://docs.docker.com/get-docker/
   ```

2. **Install Node.js**
   ```bash
   # Download from https://nodejs.org/
   # Or use a version manager like nvm
   ```

3. **Install AWS SAM CLI**
   ```bash
   # macOS
   brew install aws-sam-cli
   
   # Linux
   pip install aws-sam-cli
   
   # Windows
   # Download from https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/install-sam-cli.html
   ```

## Quick Start

1. **Clone and navigate to the backend directory**
   ```bash
   cd backend
   ```

2. **Make scripts executable**
   ```bash
   chmod +x *.sh scripts/*.sh
   ```

3. **Start the backend**
   ```bash
   ./start.sh
   ```

   This will:
   - Install Lambda function dependencies
   - Start SAM local API server
   - Make API endpoints available locally

4. **Access the API**
   - API Gateway: http://localhost:3000

5. **Stop the backend**
   ```bash
   ./stop.sh
   ```

## API Endpoints

### Authentication
- `POST /auth/signup` - User registration
- `POST /auth/signin` - User login
- `POST /auth/refresh` - Refresh JWT token

### Posts
- `GET /posts` - Get all posts
- `POST /posts` - Create a new post
- `GET /posts/{id}` - Get specific post
- `PUT /posts/{id}` - Update post
- `DELETE /posts/{id}` - Delete post
- `POST /posts/{id}/like` - Like a post
- `DELETE /posts/{id}/like` - Unlike a post

### Media
- `POST /media/upload` - Get upload URL
- `GET /media/{id}` - Get media info
- `DELETE /media/{id}` - Delete media

### Users
- `GET /users/profile` - Get user profile
- `PUT /users/profile` - Update user profile
- `GET /users/{id}` - Get user by ID
- `POST /users/{id}/follow` - Follow user
- `DELETE /users/{id}/follow` - Unfollow user

### Health
- `GET /health` - System health check

## Local Development

This SAM backend runs locally and connects to your configured AWS services or can be deployed to AWS for production use.

## Development

### Project Structure
```
backend/
├── template.yaml           # SAM template
├── package.json            # Root package.json
├── start.sh               # Startup script
├── stop.sh                # Stop script
├── src/                   # Lambda function source code
│   ├── auth/              # Authentication service
│   ├── posts/             # Posts service
│   ├── media/             # Media service
│   ├── users/             # Users service
│   └── health/            # Health check service
├── scripts/               # Utility scripts
│   ├── init-aws-services.sh
│   └── install-dependencies.sh
└── volume/                # LocalStack data persistence
```

### Manual Commands

```bash
# Install dependencies only
./scripts/install-dependencies.sh

# Start SAM local API only
sam local start-api --port 3000

# Build the SAM application
sam build

# Validate the SAM template
sam validate
```

### Environment Variables

Create a `.env` file to customize settings:

```bash
ENVIRONMENT=development
PROJECT_NAME=gameflex
AWS_REGION=us-east-1
```

## Troubleshooting

### Common Issues

1. **Port conflicts**: Make sure port 3000 is available
2. **Docker not running**: Ensure Docker is started before running the script
3. **Permission denied**: Make sure scripts are executable with `chmod +x *.sh scripts/*.sh`
4. **SAM CLI not found**: Install AWS SAM CLI following the official guide

### Logs

```bash
# View SAM local logs
# Logs are displayed in the terminal where start.sh is running
```

## Production Deployment

To deploy to AWS:

1. **Configure AWS credentials**
   ```bash
   aws configure
   ```

2. **Deploy with SAM**
   ```bash
   sam build
   sam deploy --guided
   ```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test locally with LocalStack
5. Submit a pull request

## License

MIT License - see LICENSE file for details
