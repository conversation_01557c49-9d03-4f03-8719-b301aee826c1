#!/bin/bash

# GameFlex AWS Services Initialization Script
# This script initializes AWS services for SAM local development

set -e

echo "[AWS-INIT] Initializing AWS services for GameFlex SAM backend..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[AWS-INIT]${NC} $1"
}

# Environment variables
ENVIRONMENT=${ENVIRONMENT:-development}
PROJECT_NAME=${PROJECT_NAME:-gameflex}
AWS_REGION=${AWS_REGION:-us-east-1}
AWS_ENDPOINT_URL=${AWS_ENDPOINT_URL:-http://localhost:4566}

# Test AWS CLI and LocalStack connectivity
test_aws_connection() {
    print_status "Testing AWS connection to LocalStack..."
    if aws --endpoint-url=$AWS_ENDPOINT_URL sts get-caller-identity > /dev/null 2>&1; then
        print_status "AWS CLI connection to LocalStack successful"
        return 0
    else
        print_error "Failed to connect to LocalStack"
        return 1
    fi
}

# Create Cognito User Pool and Client
create_cognito_resources() {
    print_status "Creating Cognito User Pool..."
    
    # Create User Pool
    USER_POOL_ID=$(aws --endpoint-url=$AWS_ENDPOINT_URL cognito-idp create-user-pool \
        --pool-name "${PROJECT_NAME}-users-${ENVIRONMENT}" \
        --policies "PasswordPolicy={MinimumLength=8,RequireUppercase=true,RequireLowercase=true,RequireNumbers=true,RequireSymbols=false}" \
        --auto-verified-attributes email \
        --username-attributes email \
        --query 'UserPool.Id' \
        --output text)
    
    print_status "User Pool created with ID: $USER_POOL_ID"
    
    # Create User Pool Client
    USER_POOL_CLIENT_ID=$(aws --endpoint-url=$AWS_ENDPOINT_URL cognito-idp create-user-pool-client \
        --user-pool-id $USER_POOL_ID \
        --client-name "${PROJECT_NAME}-client-${ENVIRONMENT}" \
        --generate-secret \
        --explicit-auth-flows ADMIN_NO_SRP_AUTH ALLOW_USER_PASSWORD_AUTH ALLOW_REFRESH_TOKEN_AUTH \
        --refresh-token-validity 30 \
        --access-token-validity 60 \
        --id-token-validity 60 \
        --query 'UserPoolClient.ClientId' \
        --output text)
    
    print_status "User Pool Client created with ID: $USER_POOL_CLIENT_ID"
    
    # Export for use in other scripts
    export USER_POOL_ID
    export USER_POOL_CLIENT_ID
}

# Create test users
create_test_users() {
    print_status "Creating test users..."
    
    # Developer user
    aws --endpoint-url=$AWS_ENDPOINT_URL cognito-idp admin-create-user \
        --user-pool-id $USER_POOL_ID \
        --username "<EMAIL>" \
        --user-attributes Name=email,Value=<EMAIL> Name=email_verified,Value=true Name=given_name,Value=Developer Name=family_name,Value=User \
        --temporary-password "TempPassword123!" \
        --message-action SUPPRESS > /dev/null 2>&1 || true
    
    aws --endpoint-url=$AWS_ENDPOINT_URL cognito-idp admin-set-user-password \
        --user-pool-id $USER_POOL_ID \
        --username "<EMAIL>" \
        --password "DevPassword123!" \
        --permanent > /dev/null 2>&1 || true
    
    # Admin user
    aws --endpoint-url=$AWS_ENDPOINT_URL cognito-idp admin-create-user \
        --user-pool-id $USER_POOL_ID \
        --username "<EMAIL>" \
        --user-attributes Name=email,Value=<EMAIL> Name=email_verified,Value=true Name=given_name,Value=Admin Name=family_name,Value=User \
        --temporary-password "TempPassword123!" \
        --message-action SUPPRESS > /dev/null 2>&1 || true
    
    aws --endpoint-url=$AWS_ENDPOINT_URL cognito-idp admin-set-user-password \
        --user-pool-id $USER_POOL_ID \
        --username "<EMAIL>" \
        --password "AdminPassword123!" \
        --permanent > /dev/null 2>&1 || true
    
    print_status "Test users created successfully"
}

# Create S3 buckets
create_s3_buckets() {
    print_status "Creating S3 buckets..."
    
    local buckets=(
        "${PROJECT_NAME}-media-${ENVIRONMENT}"
        "${PROJECT_NAME}-avatars-${ENVIRONMENT}"
        "${PROJECT_NAME}-temp-${ENVIRONMENT}"
    )
    
    for bucket in "${buckets[@]}"; do
        aws --endpoint-url=$AWS_ENDPOINT_URL s3 mb s3://$bucket > /dev/null 2>&1 || true
        
        # Set CORS configuration
        aws --endpoint-url=$AWS_ENDPOINT_URL s3api put-bucket-cors \
            --bucket $bucket \
            --cors-configuration '{
                "CORSRules": [{
                    "AllowedHeaders": ["*"],
                    "AllowedMethods": ["GET", "PUT", "POST", "DELETE", "HEAD"],
                    "AllowedOrigins": ["*"],
                    "MaxAgeSeconds": 3000
                }]
            }' > /dev/null 2>&1 || true
        
        print_status "Created S3 bucket: $bucket"
    done
}

# Seed DynamoDB with test data
seed_dynamodb_data() {
    print_status "Seeding DynamoDB with test data..."
    
    # Create test user records
    local user_id_1="550e8400-e29b-41d4-a716-446655440001"
    local user_id_2="550e8400-e29b-41d4-a716-446655440002"
    
    # User 1
    aws --endpoint-url=$AWS_ENDPOINT_URL dynamodb put-item \
        --table-name "${PROJECT_NAME}-${ENVIRONMENT}-Users" \
        --item '{
            "id": {"S": "'$user_id_1'"},
            "email": {"S": "<EMAIL>"},
            "username": {"S": "developer"},
            "firstName": {"S": "Developer"},
            "lastName": {"S": "User"},
            "cognito_user_id": {"S": "<EMAIL>"},
            "created_at": {"S": "'$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)'"},
            "updated_at": {"S": "'$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)'"}
        }' > /dev/null 2>&1 || true
    
    # User 2
    aws --endpoint-url=$AWS_ENDPOINT_URL dynamodb put-item \
        --table-name "${PROJECT_NAME}-${ENVIRONMENT}-Users" \
        --item '{
            "id": {"S": "'$user_id_2'"},
            "email": {"S": "<EMAIL>"},
            "username": {"S": "admin"},
            "firstName": {"S": "Admin"},
            "lastName": {"S": "User"},
            "cognito_user_id": {"S": "<EMAIL>"},
            "created_at": {"S": "'$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)'"},
            "updated_at": {"S": "'$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)'"}
        }' > /dev/null 2>&1 || true
    
    # Create user profiles
    aws --endpoint-url=$AWS_ENDPOINT_URL dynamodb put-item \
        --table-name "${PROJECT_NAME}-${ENVIRONMENT}-UserProfiles" \
        --item '{
            "user_id": {"S": "'$user_id_1'"},
            "bio": {"S": "GameFlex Developer"},
            "followersCount": {"N": "0"},
            "followingCount": {"N": "0"},
            "postsCount": {"N": "0"},
            "created_at": {"S": "'$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)'"},
            "updated_at": {"S": "'$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)'"}
        }' > /dev/null 2>&1 || true
    
    aws --endpoint-url=$AWS_ENDPOINT_URL dynamodb put-item \
        --table-name "${PROJECT_NAME}-${ENVIRONMENT}-UserProfiles" \
        --item '{
            "user_id": {"S": "'$user_id_2'"},
            "bio": {"S": "GameFlex Administrator"},
            "followersCount": {"N": "0"},
            "followingCount": {"N": "0"},
            "postsCount": {"N": "0"},
            "created_at": {"S": "'$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)'"},
            "updated_at": {"S": "'$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)'"}
        }' > /dev/null 2>&1 || true
    
    # Create a test post
    local post_id="660e8400-e29b-41d4-a716-446655440001"
    aws --endpoint-url=$AWS_ENDPOINT_URL dynamodb put-item \
        --table-name "${PROJECT_NAME}-${ENVIRONMENT}-Posts" \
        --item '{
            "id": {"S": "'$post_id'"},
            "title": {"S": "Welcome to GameFlex!"},
            "content": {"S": "This is a test post to verify the system is working correctly."},
            "userId": {"S": "'$user_id_1'"},
            "likes": {"N": "0"},
            "comments": {"N": "0"},
            "created_at": {"S": "'$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)'"},
            "updated_at": {"S": "'$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)'"}
        }' > /dev/null 2>&1 || true
    
    print_status "Test data seeded successfully"
}

# Main execution
main() {
    print_header "Initializing AWS services for GameFlex SAM backend..."
    echo
    
    if ! test_aws_connection; then
        print_error "Cannot connect to LocalStack. Make sure it's running."
        exit 1
    fi
    
    create_cognito_resources
    create_test_users
    create_s3_buckets
    seed_dynamodb_data
    
    echo
    print_status "AWS Services initialized successfully!"
    echo
    print_status "Configuration:"
    echo -e "  User Pool ID: ${CYAN}$USER_POOL_ID${NC}"
    echo -e "  Client ID: ${CYAN}$USER_POOL_CLIENT_ID${NC}"
    echo
    print_status "Test Users:"
    echo -e "  📧 Developer: <EMAIL> / DevPassword123!"
    echo -e "  👑 Admin: <EMAIL> / AdminPassword123!"
    echo
    print_status "Initialization completed!"
}

# Run main function
main "$@"
