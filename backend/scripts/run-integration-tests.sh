#!/bin/bash

# GameFlex SAM Backend Integration Test Runner
# This script runs integration tests against a running backend

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[TESTS]${NC} $1"
}

# Check if backend is running
check_backend() {
    print_status "Checking if backend is running at http://localhost:3000..."
    
    if curl -s http://localhost:3000/health > /dev/null; then
        print_status "Backend is running!"
        return 0
    else
        print_error "Backend is not running at http://localhost:3000"
        print_status "Please start the backend with: npm start"
        return 1
    fi
}

# Run integration tests
run_tests() {
    print_header "Running integration tests against backend..."
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        print_status "Installing dependencies..."
        npm install
    fi
    
    # Run the tests
    npx jest tests/integration --verbose
}

# Main execution
main() {
    print_header "GameFlex SAM Backend Integration Test Runner"
    echo
    
    if check_backend; then
        run_tests
    else
        print_error "Cannot run tests without a running backend"
        exit 1
    fi
}

# Run main function
main "$@"
