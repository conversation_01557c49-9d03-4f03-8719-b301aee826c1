#!/bin/bash

# GameFlex AWS Data Seeding Script - Comprehensive Version
# This script seeds AWS services with test data for development
# Combines the best features from both previous seeding scripts

set -e

echo "[SEED] Starting GameFlex AWS data seeding..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[SEED]${NC} $1"
}

# Load environment variables from .env file if it exists
load_env_file() {
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    local env_file="$(dirname "$script_dir")/.env"

    if [ -f "$env_file" ]; then
        print_status "Loading environment variables from .env file..."
        # Export variables from .env file, ignoring comments and empty lines
        set -a
        source "$env_file"
        set +a
        print_status "Environment variables loaded successfully"
    else
        print_warning ".env file not found at $env_file"
        print_warning "Make sure to set R2 environment variables manually"
    fi
}

# Load environment variables first
load_env_file

# Function to extract s3_prefix from samconfig.toml
get_s3_prefix() {
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    local samconfig_path="$(dirname "$script_dir")/samconfig.toml"

    if [ ! -f "$samconfig_path" ]; then
        print_error "samconfig.toml not found at: $samconfig_path"
        print_error "Please run this script from the backend directory or ensure samconfig.toml exists"
        exit 1
    fi

    # Extract s3_prefix from samconfig.toml
    local s3_prefix=$(grep -E '^s3_prefix\s*=' "$samconfig_path" | sed 's/.*=\s*"\([^"]*\)".*/\1/' | tr -d ' ')

    if [ -z "$s3_prefix" ]; then
        print_error "Could not extract s3_prefix from samconfig.toml"
        print_error "Please ensure s3_prefix is properly configured in samconfig.toml"
        exit 1
    fi

    echo "$s3_prefix"
}

# Function to extract region from samconfig.toml
get_region() {
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    local samconfig_path="$(dirname "$script_dir")/samconfig.toml"

    if [ ! -f "$samconfig_path" ]; then
        print_error "samconfig.toml not found at: $samconfig_path"
        exit 1
    fi

    # Extract region from samconfig.toml
    local region=$(grep -E '^region\s*=' "$samconfig_path" | sed 's/.*=\s*"\([^"]*\)".*/\1/' | tr -d ' ')

    if [ -z "$region" ]; then
        print_error "Could not extract region from samconfig.toml"
        print_error "Please ensure region is properly configured in samconfig.toml"
        exit 1
    fi

    echo "$region"
}

# Get configuration from samconfig.toml
S3_PREFIX=$(get_s3_prefix)
AWS_REGION=$(get_region)
print_status "Using s3_prefix from samconfig.toml: $S3_PREFIX"
print_status "Using region from samconfig.toml: $AWS_REGION"

# Environment variables (with fallbacks)
ENVIRONMENT=${ENVIRONMENT:-development}
PROJECT_NAME=${PROJECT_NAME:-gameflex}

# CloudFlare R2 Configuration
R2_ACCOUNT_ID=${R2_ACCOUNT_ID:-}
R2_ACCESS_KEY_ID=${R2_ACCESS_KEY_ID:-}
R2_SECRET_ACCESS_KEY=${R2_SECRET_ACCESS_KEY:-}
R2_ENDPOINT=${R2_ENDPOINT:-}
R2_BUCKET_NAME=${R2_BUCKET_NAME:-}
R2_PUBLIC_URL=${R2_PUBLIC_URL:-}
CLOUDFLARE_API_TOKEN=${CLOUDFLARE_API_TOKEN:-}

# Table names (using s3_prefix from SAM config)
USERS_TABLE="${S3_PREFIX}-Users"
USER_PROFILES_TABLE="${S3_PREFIX}-UserProfiles"
POSTS_TABLE="${S3_PREFIX}-Posts"
MEDIA_TABLE="${S3_PREFIX}-Media"
COMMENTS_TABLE="${S3_PREFIX}-Comments"
LIKES_TABLE="${S3_PREFIX}-Likes"
FOLLOWS_TABLE="${S3_PREFIX}-Follows"

# Note: S3 buckets removed - using CloudFlare R2 instead

# Helper functions
item_exists() {
    local table_name=$1
    local key_condition=$2

    # Check if item exists by getting the item and checking if we get a result
    local item_result=$(aws dynamodb get-item \
        --table-name "$table_name" \
        --key "$key_condition" \
        --region "$AWS_REGION" \
        --query 'Item' \
        --output text 2>/dev/null)

    # Return success if we got a result and it's not empty/null
    [ $? -eq 0 ] && [ -n "$item_result" ] && [ "$item_result" != "None" ]
}

put_item() {
    local table_name=$1
    local item_json=$2
    local item_description=$3

    # Check if item already exists for certain tables
    local should_check_exists=false
    local key_condition=""
    
    case "$table_name" in
        *Users)
            should_check_exists=true
            local user_id=$(echo "$item_json" | jq -r '.id.S // empty')
            if [ -n "$user_id" ]; then
                key_condition='{"id":{"S":"'$user_id'"}}'
            fi
            ;;
        *UserProfiles)
            should_check_exists=true
            local user_id=$(echo "$item_json" | jq -r '.user_id.S // empty')
            if [ -n "$user_id" ]; then
                key_condition='{"user_id":{"S":"'$user_id'"}}'
            fi
            ;;
        *Posts)
            should_check_exists=true
            local post_id=$(echo "$item_json" | jq -r '.id.S // empty')
            if [ -n "$post_id" ]; then
                key_condition='{"id":{"S":"'$post_id'"}}'
            fi
            ;;
        *Media)
            should_check_exists=true
            local media_id=$(echo "$item_json" | jq -r '.id.S // empty')
            if [ -n "$media_id" ]; then
                key_condition='{"id":{"S":"'$media_id'"}}'
            fi
            ;;
    esac

    # Check if item exists and skip if it does
    if [ "$should_check_exists" = true ] && [ -n "$key_condition" ]; then
        if item_exists "$table_name" "$key_condition"; then
            print_status "$item_description already exists, skipping"
            return 0
        fi
    fi

    print_status "Adding $item_description to $table_name..."
    
    local temp_file=$(mktemp)
    echo "$item_json" > "$temp_file"
    
    if timeout 30 aws dynamodb put-item \
        --table-name "$table_name" \
        --region "$AWS_REGION" \
        --item file://"$temp_file" > /dev/null 2>&1; then
        print_status "✅ Successfully added $item_description"
    else
        print_error "❌ Failed to add $item_description"
        # Show the actual error for debugging
        echo "Error details:" >&2
        timeout 30 aws dynamodb put-item \
            --table-name "$table_name" \
            --region "$AWS_REGION" \
            --item file://"$temp_file" 2>&1 | head -3 >&2
        rm -f "$temp_file"
        return 1
    fi
    
    rm -f "$temp_file"
    return 0
}

# Test AWS connection
test_aws_connection() {
    print_status "Testing AWS connection..."
    if aws sts get-caller-identity > /dev/null 2>&1; then
        local account_id=$(aws sts get-caller-identity --query Account --output text)
        local region=$(aws configure get region || echo $AWS_REGION)
        print_status "AWS CLI connection successful"
        print_status "Account ID: $account_id"
        print_status "Region: $region"
        return 0
    else
        print_error "Failed to connect to AWS"
        return 1
    fi
}

# Check if required tables exist
check_tables() {
    print_status "Checking if required tables exist..."

    local tables=("$USERS_TABLE" "$USER_PROFILES_TABLE" "$POSTS_TABLE" "$MEDIA_TABLE" "$COMMENTS_TABLE" "$LIKES_TABLE" "$FOLLOWS_TABLE")
    local missing_tables=()

    print_status "Table names being checked:"
    for table in "${tables[@]}"; do
        print_status "  - $table"
    done
    echo
    
    for table in "${tables[@]}"; do
        # Check if table exists by trying to describe it and checking the output
        table_status=$(aws dynamodb describe-table --table-name "$table" --region "$AWS_REGION" --query 'Table.TableStatus' --output text 2>/dev/null)
        exit_code=$?

        if [ $exit_code -eq 0 ] && [ -n "$table_status" ] && [ "$table_status" != "None" ]; then
            print_status "✅ Table $table exists (Status: $table_status)"
        else
            print_warning "❌ Table $table does not exist or is not accessible"
            missing_tables+=("$table")
        fi
    done
    
    if [ ${#missing_tables[@]} -gt 0 ]; then
        print_error "Missing tables: ${missing_tables[*]}"
        print_error "Make sure your SAM application is deployed: sam build && sam deploy"
        return 1
    fi
    
    return 0
}

# Check CloudFlare R2 connectivity
check_r2_connectivity() {
    print_status "Checking CloudFlare R2 connectivity..."

    # Check if R2 environment variables are set
    if [ -z "$R2_ACCESS_KEY_ID" ] || [ -z "$R2_SECRET_ACCESS_KEY" ] || [ -z "$R2_ENDPOINT" ] || [ -z "$R2_BUCKET_NAME" ]; then
        print_error "R2 configuration missing. Please ensure the following environment variables are set:"
        print_error "  R2_ACCESS_KEY_ID"
        print_error "  R2_SECRET_ACCESS_KEY"
        print_error "  R2_ENDPOINT"
        print_error "  R2_BUCKET_NAME"
        print_error "Check your .env file or environment variables"
        return 1
    fi

    print_status "R2 Configuration:"
    print_status "  Endpoint: $R2_ENDPOINT"
    print_status "  Bucket: $R2_BUCKET_NAME"
    print_status "  Public URL: $R2_PUBLIC_URL"

    # Test R2 connectivity using AWS CLI with R2 endpoint
    print_status "Testing R2 bucket access..."

    # Configure AWS CLI to use R2 credentials temporarily
    export AWS_ACCESS_KEY_ID="$R2_ACCESS_KEY_ID"
    export AWS_SECRET_ACCESS_KEY="$R2_SECRET_ACCESS_KEY"

    # Test bucket access
    if aws s3 ls "s3://$R2_BUCKET_NAME" --endpoint-url "$R2_ENDPOINT" --region auto 2>/dev/null; then
        print_status "✅ R2 bucket '$R2_BUCKET_NAME' is accessible"

        # Test upload capability with a small test file
        local test_file=$(mktemp)
        echo "test" > "$test_file"
        local test_key="test/connectivity-test-$(date +%s).txt"

        if aws s3 cp "$test_file" "s3://$R2_BUCKET_NAME/$test_key" --endpoint-url "$R2_ENDPOINT" --region auto 2>/dev/null; then
            print_status "✅ R2 upload test successful"

            # Clean up test file
            aws s3 rm "s3://$R2_BUCKET_NAME/$test_key" --endpoint-url "$R2_ENDPOINT" --region auto 2>/dev/null
            print_status "✅ R2 delete test successful"
        else
            print_warning "❌ R2 upload test failed - check permissions"
        fi

        rm -f "$test_file"
    else
        print_error "❌ Cannot access R2 bucket '$R2_BUCKET_NAME'"
        print_error "Please check your R2 credentials and bucket configuration"
        return 1
    fi

    # Restore original AWS credentials for DynamoDB operations
    unset AWS_ACCESS_KEY_ID
    unset AWS_SECRET_ACCESS_KEY

    return 0
}

# Main execution starts here
main() {
    print_header "Starting comprehensive GameFlex data seeding..."
    echo
    
    # Test AWS connection
    if ! test_aws_connection; then
        exit 1
    fi
    echo
    
    # Check if tables exist
    if ! check_tables; then
        exit 1
    fi
    echo

    # Check R2 connectivity
    if ! check_r2_connectivity; then
        exit 1
    fi
    echo
    
    # Debug: Show configuration
    print_status "Using configuration:"
    print_status "  S3 Prefix: $S3_PREFIX"
    print_status "  Environment: $ENVIRONMENT"
    print_status "  Project: $PROJECT_NAME"
    print_status "  Region: $AWS_REGION"
    print_status "  Users Table: $USERS_TABLE"
    print_status "  Posts Table: $POSTS_TABLE"
    print_status "  Media Table: $MEDIA_TABLE"
    print_status "  R2 Bucket: $R2_BUCKET_NAME"
    print_status "  R2 Public URL: $R2_PUBLIC_URL"
    echo
    
    # Start seeding
    seed_users
    echo
    seed_user_profiles
    echo
    seed_media
    echo
    seed_posts
    echo
    seed_comments
    echo
    seed_likes
    echo
    seed_follows
    echo
    upload_media_files_to_r2
    echo

    print_status "🎉 Complete seeding finished successfully!"
    print_summary
}

# Seed Users table
seed_users() {
    print_header "Seeding Users table..."

    put_item "$USERS_TABLE" '{
        "id": {"S": "550e8400-e29b-41d4-a716-************"},
        "cognito_user_id": {"S": "dev-cognito-placeholder"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "developer"},
        "display_name": {"S": "GameFlex Developer"},
        "bio": {"S": "Development account for testing GameFlex features"},
        "is_active": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }' "developer user"

    put_item "$USERS_TABLE" '{
        "id": {"S": "550e8400-e29b-41d4-a716-************"},
        "cognito_user_id": {"S": "admin-cognito-placeholder"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "admin"},
        "display_name": {"S": "GameFlex Admin"},
        "bio": {"S": "Administrator account with full access"},
        "is_active": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }' "admin user"

    put_item "$USERS_TABLE" '{
        "id": {"S": "********-0000-0000-0000-********0003"},
        "cognito_user_id": {"S": "john-cognito-placeholder"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "johndoe"},
        "display_name": {"S": "John Doe"},
        "bio": {"S": "Gaming enthusiast and content creator."},
        "is_active": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }' "john doe user"

    put_item "$USERS_TABLE" '{
        "id": {"S": "********-0000-0000-0000-********0004"},
        "cognito_user_id": {"S": "jane-cognito-placeholder"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "janesmith"},
        "display_name": {"S": "Jane Smith"},
        "bio": {"S": "Professional gamer and streamer."},
        "is_active": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }' "jane smith user"

    put_item "$USERS_TABLE" '{
        "id": {"S": "********-0000-0000-0000-********0005"},
        "cognito_user_id": {"S": "mike-cognito-placeholder"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "mikewilson"},
        "display_name": {"S": "Mike Wilson"},
        "bio": {"S": "Casual gamer who loves sharing gaming moments."},
        "is_active": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }' "mike wilson user"

    print_status "Users seeded successfully!"
}

# Seed UserProfiles table
seed_user_profiles() {
    print_header "Seeding UserProfiles table..."

    put_item "$USER_PROFILES_TABLE" '{
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "first_name": {"S": "Dev"},
        "last_name": {"S": "User"},
        "country": {"S": "United States"},
        "timezone": {"S": "America/New_York"},
        "language": {"S": "en"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }' "dev profile"

    put_item "$USER_PROFILES_TABLE" '{
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "first_name": {"S": "Admin"},
        "last_name": {"S": "User"},
        "country": {"S": "United States"},
        "timezone": {"S": "America/Los_Angeles"},
        "language": {"S": "en"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }' "admin profile"

    put_item "$USER_PROFILES_TABLE" '{
        "user_id": {"S": "********-0000-0000-0000-********0003"},
        "first_name": {"S": "John"},
        "last_name": {"S": "Doe"},
        "country": {"S": "United States"},
        "timezone": {"S": "America/New_York"},
        "language": {"S": "en"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }' "john profile"

    put_item "$USER_PROFILES_TABLE" '{
        "user_id": {"S": "********-0000-0000-0000-********0004"},
        "first_name": {"S": "Jane"},
        "last_name": {"S": "Smith"},
        "country": {"S": "United States"},
        "timezone": {"S": "America/Los_Angeles"},
        "language": {"S": "en"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }' "jane profile"

    put_item "$USER_PROFILES_TABLE" '{
        "user_id": {"S": "********-0000-0000-0000-********0005"},
        "first_name": {"S": "Mike"},
        "last_name": {"S": "Wilson"},
        "country": {"S": "United States"},
        "timezone": {"S": "America/Chicago"},
        "language": {"S": "en"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }' "mike profile"

    print_status "User profiles seeded successfully!"
}

# Seed Media table
seed_media() {
    print_header "Seeding Media table..."

    put_item "$MEDIA_TABLE" '{
        "id": {"S": "30000000-0000-0000-0000-********0001"},
        "fileName": {"S": "cod_screenshot.jpg"},
        "fileType": {"S": "image/jpeg"},
        "fileSize": {"N": "245760"},
        "mediaType": {"S": "image"},
        "userId": {"S": "********-0000-0000-0000-********0003"},
        "r2Key": {"S": "user/********-0000-0000-0000-********0003/cod_screenshot.jpg"},
        "bucketName": {"S": "'$R2_BUCKET_NAME'"},
        "url": {"S": "'$R2_PUBLIC_URL'/user/********-0000-0000-0000-********0003/cod_screenshot.jpg"},
        "status": {"S": "uploaded"},
        "created_at": {"S": "2024-12-28T14:00:00Z"},
        "updated_at": {"S": "2024-12-28T14:00:00Z"}
    }' "cod screenshot media"

    put_item "$MEDIA_TABLE" '{
        "id": {"S": "30000000-0000-0000-0000-********0002"},
        "fileName": {"S": "diablo_screenshot.webp"},
        "fileType": {"S": "image/webp"},
        "fileSize": {"N": "189440"},
        "mediaType": {"S": "image"},
        "userId": {"S": "********-0000-0000-0000-********0004"},
        "r2Key": {"S": "user/********-0000-0000-0000-********0004/diablo_screenshot.webp"},
        "bucketName": {"S": "'$R2_BUCKET_NAME'"},
        "url": {"S": "'$R2_PUBLIC_URL'/user/********-0000-0000-0000-********0004/diablo_screenshot.webp"},
        "status": {"S": "uploaded"},
        "created_at": {"S": "2024-12-27T16:00:00Z"},
        "updated_at": {"S": "2024-12-27T16:00:00Z"}
    }' "diablo screenshot media"

    print_status "Media seeded successfully!"
}

# Seed Posts table (using new structure with media_id references)
seed_posts() {
    print_header "Seeding Posts table..."

    put_item "$POSTS_TABLE" '{
        "id": {"S": "20000000-0000-0000-0000-********0001"},
        "author_id": {"S": "********-0000-0000-0000-********0003"},
        "userId": {"S": "********-0000-0000-0000-********0003"},
        "content": {"S": "Just had an epic Call of Duty session! Check out this clutch moment 🎮🔥"},
        "media_id": {"S": "30000000-0000-0000-0000-********0001"},
        "likes": {"N": "12"},
        "comments": {"N": "4"},
        "created_at": {"S": "2024-12-28T14:30:00Z"},
        "updated_at": {"S": "2024-12-28T14:30:00Z"}
    }' "john cod post"

    put_item "$POSTS_TABLE" '{
        "id": {"S": "20000000-0000-0000-0000-********0002"},
        "author_id": {"S": "********-0000-0000-0000-********0004"},
        "userId": {"S": "********-0000-0000-0000-********0004"},
        "content": {"S": "Finally defeated this boss in Diablo! The loot was totally worth the grind 💀⚔️"},
        "media_id": {"S": "30000000-0000-0000-0000-********0002"},
        "likes": {"N": "18"},
        "comments": {"N": "6"},
        "created_at": {"S": "2024-12-27T16:45:00Z"},
        "updated_at": {"S": "2024-12-27T16:45:00Z"}
    }' "jane diablo post"

    # Add a text-only post
    put_item "$POSTS_TABLE" '{
        "id": {"S": "20000000-0000-0000-0000-********0003"},
        "author_id": {"S": "********-0000-0000-0000-********0005"},
        "userId": {"S": "********-0000-0000-0000-********0005"},
        "content": {"S": "Anyone else excited for the new gaming releases this month? What are you most looking forward to? 🎮"},
        "likes": {"N": "5"},
        "comments": {"N": "2"},
        "created_at": {"S": "2024-12-26T10:15:00Z"},
        "updated_at": {"S": "2024-12-26T10:15:00Z"}
    }' "mike text post"

    print_status "Posts seeded successfully!"
}

# Seed Comments table
seed_comments() {
    print_header "Seeding Comments table..."

    put_item "$COMMENTS_TABLE" '{
        "id": {"S": "40000000-0000-0000-0000-********0001"},
        "post_id": {"S": "20000000-0000-0000-0000-********0001"},
        "user_id": {"S": "********-0000-0000-0000-********0004"},
        "content": {"S": "Nice clutch! What loadout were you using?"},
        "like_count": {"N": "3"},
        "is_active": {"BOOL": true},
        "created_at": {"S": "2024-12-28T15:15:00Z"},
        "updated_at": {"S": "2024-12-28T15:15:00Z"}
    }' "comment 1"

    put_item "$COMMENTS_TABLE" '{
        "id": {"S": "40000000-0000-0000-0000-********0002"},
        "post_id": {"S": "20000000-0000-0000-0000-********0001"},
        "user_id": {"S": "********-0000-0000-0000-********0005"},
        "content": {"S": "That was insane! 🔥"},
        "like_count": {"N": "1"},
        "is_active": {"BOOL": true},
        "created_at": {"S": "2024-12-28T16:20:00Z"},
        "updated_at": {"S": "2024-12-28T16:20:00Z"}
    }' "comment 2"

    put_item "$COMMENTS_TABLE" '{
        "id": {"S": "40000000-0000-0000-0000-********0003"},
        "post_id": {"S": "20000000-0000-0000-0000-********0002"},
        "user_id": {"S": "********-0000-0000-0000-********0003"},
        "content": {"S": "Congrats! What difficulty level?"},
        "like_count": {"N": "2"},
        "is_active": {"BOOL": true},
        "created_at": {"S": "2024-12-27T17:30:00Z"},
        "updated_at": {"S": "2024-12-27T17:30:00Z"}
    }' "comment 3"

    put_item "$COMMENTS_TABLE" '{
        "id": {"S": "40000000-0000-0000-0000-********0004"},
        "post_id": {"S": "20000000-0000-0000-0000-********0002"},
        "user_id": {"S": "********-0000-0000-0000-********0005"},
        "content": {"S": "The loot looks amazing! 💎"},
        "like_count": {"N": "4"},
        "is_active": {"BOOL": true},
        "created_at": {"S": "2024-12-27T18:15:00Z"},
        "updated_at": {"S": "2024-12-27T18:15:00Z"}
    }' "comment 4"

    put_item "$COMMENTS_TABLE" '{
        "id": {"S": "40000000-0000-0000-0000-********0005"},
        "post_id": {"S": "20000000-0000-0000-0000-********0003"},
        "user_id": {"S": "********-0000-0000-0000-********0003"},
        "content": {"S": "I am really excited for the new RPG releases!"},
        "like_count": {"N": "1"},
        "is_active": {"BOOL": true},
        "created_at": {"S": "2024-12-26T11:00:00Z"},
        "updated_at": {"S": "2024-12-26T11:00:00Z"}
    }' "comment 5"

    print_status "Comments seeded successfully!"
}

# Seed Likes table
seed_likes() {
    print_header "Seeding Likes table..."

    # Likes for post 1 (John's COD post)
    put_item "$LIKES_TABLE" '{
        "post_id": {"S": "20000000-0000-0000-0000-********0001"},
        "user_id": {"S": "********-0000-0000-0000-********0004"},
        "created_at": {"S": "2024-12-28T14:35:00Z"}
    }' "like 1"

    put_item "$LIKES_TABLE" '{
        "post_id": {"S": "20000000-0000-0000-0000-********0001"},
        "user_id": {"S": "********-0000-0000-0000-********0005"},
        "created_at": {"S": "2024-12-28T14:40:00Z"}
    }' "like 2"

    put_item "$LIKES_TABLE" '{
        "post_id": {"S": "20000000-0000-0000-0000-********0001"},
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "created_at": {"S": "2024-12-28T14:45:00Z"}
    }' "like 3"

    # Likes for post 2 (Jane's Diablo post)
    put_item "$LIKES_TABLE" '{
        "post_id": {"S": "20000000-0000-0000-0000-********0002"},
        "user_id": {"S": "********-0000-0000-0000-********0003"},
        "created_at": {"S": "2024-12-27T16:50:00Z"}
    }' "like 4"

    put_item "$LIKES_TABLE" '{
        "post_id": {"S": "20000000-0000-0000-0000-********0002"},
        "user_id": {"S": "********-0000-0000-0000-********0005"},
        "created_at": {"S": "2024-12-27T17:00:00Z"}
    }' "like 5"

    put_item "$LIKES_TABLE" '{
        "post_id": {"S": "20000000-0000-0000-0000-********0002"},
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "created_at": {"S": "2024-12-27T17:15:00Z"}
    }' "like 6"

    # Likes for post 3 (Mike's text post)
    put_item "$LIKES_TABLE" '{
        "post_id": {"S": "20000000-0000-0000-0000-********0003"},
        "user_id": {"S": "********-0000-0000-0000-********0003"},
        "created_at": {"S": "2024-12-26T10:30:00Z"}
    }' "like 7"

    put_item "$LIKES_TABLE" '{
        "post_id": {"S": "20000000-0000-0000-0000-********0003"},
        "user_id": {"S": "********-0000-0000-0000-********0004"},
        "created_at": {"S": "2024-12-26T11:15:00Z"}
    }' "like 8"

    print_status "Likes seeded successfully!"
}

# Seed Follows table
seed_follows() {
    print_header "Seeding Follows table..."

    put_item "$FOLLOWS_TABLE" '{
        "follower_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "following_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "created_at": {"S": "2024-01-01T00:00:00Z"}
    }' "dev follows admin"

    put_item "$FOLLOWS_TABLE" '{
        "follower_id": {"S": "********-0000-0000-0000-********0003"},
        "following_id": {"S": "********-0000-0000-0000-********0004"},
        "created_at": {"S": "2024-01-02T00:00:00Z"}
    }' "john follows jane"

    put_item "$FOLLOWS_TABLE" '{
        "follower_id": {"S": "********-0000-0000-0000-********0004"},
        "following_id": {"S": "********-0000-0000-0000-********0005"},
        "created_at": {"S": "2024-01-03T00:00:00Z"}
    }' "jane follows mike"

    put_item "$FOLLOWS_TABLE" '{
        "follower_id": {"S": "********-0000-0000-0000-********0005"},
        "following_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "created_at": {"S": "2024-01-04T00:00:00Z"}
    }' "mike follows dev"

    put_item "$FOLLOWS_TABLE" '{
        "follower_id": {"S": "********-0000-0000-0000-********0003"},
        "following_id": {"S": "********-0000-0000-0000-********0005"},
        "created_at": {"S": "2024-01-05T00:00:00Z"}
    }' "john follows mike"

    print_status "Follows seeded successfully!"
}

# Upload media files to CloudFlare R2
upload_media_files_to_r2() {
    print_header "Uploading media files to CloudFlare R2..."

    # Function to upload file to R2
    upload_r2_file() {
        local file_path=$1
        local r2_key=$2
        local content_type=$3

        if [ ! -f "$file_path" ]; then
            print_warning "Media file not found: $file_path (skipping)"
            return 0
        fi

        # Configure AWS CLI to use R2 credentials temporarily
        export AWS_ACCESS_KEY_ID="$R2_ACCESS_KEY_ID"
        export AWS_SECRET_ACCESS_KEY="$R2_SECRET_ACCESS_KEY"

        # Check if file already exists in R2
        if aws s3 ls "s3://$R2_BUCKET_NAME/$r2_key" --endpoint-url "$R2_ENDPOINT" --region auto 2>/dev/null; then
            print_status "File $r2_key already exists in R2, skipping"
            # Restore original AWS credentials
            unset AWS_ACCESS_KEY_ID
            unset AWS_SECRET_ACCESS_KEY
            return 0
        fi

        print_status "Uploading $file_path to R2: s3://$R2_BUCKET_NAME/$r2_key"

        if aws s3 cp "$file_path" "s3://$R2_BUCKET_NAME/$r2_key" \
            --endpoint-url "$R2_ENDPOINT" \
            --region auto \
            --content-type "$content_type" > /dev/null 2>&1; then
            print_status "✅ Successfully uploaded $r2_key to R2"
        else
            print_warning "❌ Failed to upload $r2_key to R2 (continuing anyway)"
        fi

        # Restore original AWS credentials for DynamoDB operations
        unset AWS_ACCESS_KEY_ID
        unset AWS_SECRET_ACCESS_KEY
    }

    # Get the script directory to find assets
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    ASSETS_DIR="$(dirname "$SCRIPT_DIR")/assets/media"

    # Upload media files for seeded posts
    print_status "Looking for media files in: $ASSETS_DIR"

    upload_r2_file \
        "$ASSETS_DIR/cod_screenshot.jpg" \
        "user/********-0000-0000-0000-********0003/cod_screenshot.jpg" \
        "image/jpeg"

    upload_r2_file \
        "$ASSETS_DIR/diablo_screenshot.webp" \
        "user/********-0000-0000-0000-********0004/diablo_screenshot.webp" \
        "image/webp"

    print_status "R2 media upload completed!"
}

# Print summary
print_summary() {
    print_status "Summary:"
    print_status "  📊 Seeded 5 users with profiles"
    print_status "  📝 Seeded 3 posts (2 with media, 1 text-only)"
    print_status "  💬 Seeded 5 comments"
    print_status "  ❤️  Seeded 8 likes"
    print_status "  👥 Seeded 5 follow relationships"
    print_status "  📸 Seeded 2 media items (R2 URLs)"
    print_status "  🔗 Uploaded media files to CloudFlare R2"
    print_status ""
    print_status "Your GameFlex backend is now ready for testing!"
    print_status "API Gateway URL: Check your SAM deployment outputs"
    print_status ""
    print_status "CloudFlare R2 Configuration:"
    print_status "  Bucket: $R2_BUCKET_NAME"
    print_status "  Public URL: $R2_PUBLIC_URL"
    print_status ""
    print_status "Test the posts endpoint:"
    print_status "  curl https://your-api-gateway-url/posts"
}

# Check if jq is installed
if ! command -v jq &> /dev/null; then
    print_error "jq is required but not installed. Please install jq first."
    print_error "Ubuntu/Debian: sudo apt-get install jq"
    print_error "macOS: brew install jq"
    exit 1
fi

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
