#!/bin/bash

# GameFlex SAM Backend Test Runner
# This script runs the test suite with different configurations

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[TESTS]${NC} $1"
}

# Check if npm is installed
check_npm() {
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install Node.js and npm first."
        exit 1
    fi
    print_status "npm is available"
}

# Install test dependencies
install_dependencies() {
    print_status "Installing test dependencies..."
    npm install
    print_status "Dependencies installed"
}

# Run specific test suite
run_test_suite() {
    local suite=$1
    local description=$2
    
    print_header "Running $description"
    
    case $suite in
        "unit")
            npm run test:unit
            ;;
        "integration")
            npm run test:integration
            ;;
        "coverage")
            npm run test:coverage
            ;;
        "all")
            npm test
            ;;
        "watch")
            npm run test:watch
            ;;
        *)
            print_error "Unknown test suite: $suite"
            exit 1
            ;;
    esac
}

# Display test results summary
display_summary() {
    print_header "Test Summary"
    echo
    print_status "Test suites available:"
    echo "  🧪 Unit Tests: npm run test:unit"
    echo "  🔗 Integration Tests: npm run test:integration"
    echo "  📊 Coverage Report: npm run test:coverage"
    echo "  🔄 Watch Mode: npm run test:watch"
    echo "  🎯 All Tests: npm test"
    echo
    print_status "Individual test files can be run with:"
    echo "  npx jest tests/unit/health.test.js"
    echo "  npx jest tests/unit/auth.test.js"
    echo "  npx jest tests/unit/posts.test.js"
    echo "  npx jest tests/unit/media.test.js"
    echo "  npx jest tests/unit/users.test.js"
    echo "  npx jest tests/integration/api-integration.test.js"
}

# Main execution
main() {
    print_header "GameFlex SAM Backend Test Runner"
    echo
    
    # Parse command line arguments
    local test_suite=${1:-"help"}
    
    case $test_suite in
        "help"|"-h"|"--help")
            echo "Usage: $0 [test_suite]"
            echo
            echo "Available test suites:"
            echo "  unit        - Run unit tests only"
            echo "  integration - Run integration tests only"
            echo "  coverage    - Run tests with coverage report"
            echo "  all         - Run all tests (default)"
            echo "  watch       - Run tests in watch mode"
            echo "  help        - Show this help message"
            echo
            display_summary
            exit 0
            ;;
        "unit")
            check_npm
            install_dependencies
            run_test_suite "unit" "Unit Tests"
            ;;
        "integration")
            check_npm
            install_dependencies
            run_test_suite "integration" "Integration Tests"
            ;;
        "coverage")
            check_npm
            install_dependencies
            run_test_suite "coverage" "Tests with Coverage Report"
            ;;
        "all")
            check_npm
            install_dependencies
            run_test_suite "all" "All Tests"
            ;;
        "watch")
            check_npm
            install_dependencies
            run_test_suite "watch" "Tests in Watch Mode"
            ;;
        *)
            print_error "Unknown test suite: $test_suite"
            echo "Use '$0 help' to see available options"
            exit 1
            ;;
    esac
    
    print_status "Test execution completed!"
}

# Run main function
main "$@"
