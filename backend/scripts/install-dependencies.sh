#!/bin/bash

# GameFlex Backend Dependencies Installation Script
# This script installs dependencies for all Lambda functions

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[DEPS]${NC} $1"
}

# Check if npm is installed
check_npm() {
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install Node.js and npm first."
        exit 1
    fi
    print_status "npm is available"
}

# Install dependencies for a Lambda function
install_lambda_deps() {
    local lambda_dir=$1
    local lambda_name=$(basename $lambda_dir)
    
    if [ -f "$lambda_dir/package.json" ]; then
        print_status "Installing dependencies for $lambda_name..."
        cd "$lambda_dir"
        npm install --production
        cd - > /dev/null
        print_status "✓ Dependencies installed for $lambda_name"
    else
        print_warning "No package.json found in $lambda_dir"
    fi
}

# Main execution
main() {
    print_header "Installing dependencies for GameFlex Lambda functions..."
    echo
    
    check_npm
    
    # Get the directory where this script is located
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    BACKEND_DIR="$(dirname "$SCRIPT_DIR")"
    SRC_DIR="$BACKEND_DIR/src"
    
    print_status "Backend directory: $BACKEND_DIR"
    print_status "Source directory: $SRC_DIR"
    
    # Install dependencies for each Lambda function
    local lambda_functions=(
        "$SRC_DIR/auth"
        "$SRC_DIR/posts"
        "$SRC_DIR/media"
        "$SRC_DIR/users"
        "$SRC_DIR/health"
    )
    
    for lambda_dir in "${lambda_functions[@]}"; do
        if [ -d "$lambda_dir" ]; then
            install_lambda_deps "$lambda_dir"
        else
            print_warning "Lambda directory not found: $lambda_dir"
        fi
    done
    
    echo
    print_status "All dependencies installed successfully!"
}

# Run main function
main "$@"
