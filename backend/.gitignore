# AWS SAM specific
.aws-sam/
samconfig.toml
packaged.yaml
packaged-template.yaml

# Node.js dependencies
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
package-lock.json
yarn.lock
.pnpm-debug.log

# Lambda deployment packages
*.zip

# Environment variables
.env
.env.local
.env.*.local
.env.development
.env.test
.env.production

# Build output
dist/
build/
.build/
out/

# Coverage reports
coverage/
.nyc_output/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# VSCode settings
.vscode/

# JetBrains IDEs
.idea/
*.iml
*.iws
*.ipr
.idea_modules/
out/

# macOS
.DS_Store
.AppleDouble
.LSOverride
._*

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# SAM local API
.sam_pid

# Temporary files
*.tmp
*.temp
temp/
tmp/

# CloudFormation outputs
cfn-outputs.json

# AWS CDK
cdk.out/

# Local DynamoDB
.dynamodb/

# Swagger
swagger.yaml
swagger.json

# Backup files
*.bak
*.backup
*~

# Debug files
.debug/
debug/

# Test files
.test/
test-results/

# Local development
volume/
