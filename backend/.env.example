# GameFlex SAM Backend Environment Variables
# Copy this file to .env and modify as needed

# Environment settings
ENVIRONMENT=development
PROJECT_NAME=gameflex

# AWS Configuration
AWS_REGION=us-east-1

# API Gateway settings
API_PORT=3000

# Debug settings
DEBUG=1

# AWS Resource IDs (from deployed CloudFormation stack)
USER_POOL_ID=
USER_POOL_CLIENT_ID=
USERS_TABLE=gameflex-development-Users
POSTS_TABLE=gameflex-development-Posts
MEDIA_TABLE=gameflex-development-Media
USER_PROFILES_TABLE=gameflex-development-UserProfiles
COMMENTS_TABLE=gameflex-development-Comments
LIKES_TABLE=gameflex-development-Likes
FOLLOWS_TABLE=gameflex-development-Follows
# CloudFlare R2 Configuration
R2_ACCOUNT_ID=
R2_ACCESS_KEY_ID=
R2_SECRET_ACCESS_KEY=
R2_ENDPOINT=
R2_BUCKET_NAME=gameflex-development
R2_PUBLIC_URL=

# CloudFlare API Configuration
CLOUDFLARE_API_TOKEN=
