/**
 * Mock Lambda handlers for testing
 * This file provides mock implementations of the Lambda handlers
 * to avoid import errors during testing
 */

// Create a basic response helper
const createResponse = (statusCode, body) => ({
  statusCode,
  headers: {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
    'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
  },
  body: JSON.stringify(body)
});

// Mock health handler
const healthHandler = async (event) => {
  try {
    if (event.httpMethod === 'GET' && event.path === '/health') {
      // Test DynamoDB connection (this will be mocked in tests)
      let dbStatus = 'healthy';
      try {
        const AWS = require('../utils/aws-mocks');
        await AWS.mockDynamoDBDocumentClient.scan().promise();
      } catch (error) {
        dbStatus = 'unhealthy';
      }

      return createResponse(200, {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        environment: process.env.ENVIRONMENT || 'test',
        version: '1.0.0',
        services: {
          database: dbStatus,
          api: 'healthy'
        },
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        environment_variables: {
          USER_POOL_ID: process.env.USER_POOL_ID ? 'set' : 'not set',
          USER_POOL_CLIENT_ID: process.env.USER_POOL_CLIENT_ID ? 'set' : 'not set',
          USERS_TABLE: process.env.USERS_TABLE ? 'set' : 'not set',
          POSTS_TABLE: process.env.POSTS_TABLE ? 'set' : 'not set',
          MEDIA_BUCKET: process.env.MEDIA_BUCKET ? 'set' : 'not set'
        }
      });
    }
    return createResponse(404, { error: 'Not found' });
  } catch (error) {
    return createResponse(500, {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
};

// Mock auth handler
const authHandler = async (event) => {
  try {
    let body = {};
    if (event.body) {
      try {
        body = JSON.parse(event.body);
      } catch (e) {
        return createResponse(500, { error: 'Internal server error' });
      }
    }

    if (event.httpMethod === 'POST' && event.path === '/auth/signup') {
      // Validate required fields
      if (!body.email || !body.password || !body.username) {
        return createResponse(400, { error: 'Email, password, and username are required' });
      }

      // Check for mocked Cognito errors
      const AWS = require('../utils/aws-mocks');
      try {
        await AWS.mockCognitoIdentityServiceProvider.adminCreateUser().promise();
        await AWS.mockDynamoDBDocumentClient.put().promise();
      } catch (error) {
        if (error.message === 'User already exists') {
          return createResponse(500, { error: 'Failed to create user', details: 'User already exists' });
        }
        throw error;
      }

      return createResponse(201, {
        message: 'User created successfully',
        user: {
          id: 'test-id',
          email: body.email,
          username: body.username,
          firstName: body.firstName,
          lastName: body.lastName
        }
      });
    } else if (event.httpMethod === 'POST' && event.path === '/auth/signin') {
      // Validate required fields
      if (!body.email || !body.password) {
        return createResponse(400, { error: 'Email and password are required' });
      }

      // Check for mocked Cognito errors
      const AWS = require('../utils/aws-mocks');
      try {
        await AWS.mockCognitoIdentityServiceProvider.adminInitiateAuth().promise();
        const queryResult = await AWS.mockDynamoDBDocumentClient.query().promise();

        if (!queryResult.Items || queryResult.Items.length === 0) {
          return createResponse(404, { error: 'User not found' });
        }
      } catch (error) {
        if (error.message === 'Incorrect username or password') {
          return createResponse(401, { error: 'Authentication failed', details: 'Incorrect username or password' });
        }
        throw error;
      }

      return createResponse(200, {
        message: 'Sign in successful',
        tokens: {
          accessToken: 'test-token',
          refreshToken: 'test-refresh',
          idToken: 'test-id-token'
        },
        user: { id: 'test-id', email: body.email }
      });
    } else if (event.httpMethod === 'POST' && event.path === '/auth/refresh') {
      // Validate required fields
      if (!body.refreshToken) {
        return createResponse(400, { error: 'Refresh token is required' });
      }

      // Check for mocked Cognito errors
      const AWS = require('../utils/aws-mocks');
      try {
        await AWS.mockCognitoIdentityServiceProvider.adminInitiateAuth().promise();
      } catch (error) {
        if (error.message === 'Invalid refresh token') {
          return createResponse(401, { error: 'Token refresh failed', details: 'Invalid refresh token' });
        }
        throw error;
      }

      return createResponse(200, {
        message: 'Token refreshed successfully',
        tokens: { accessToken: 'new-token', idToken: 'new-id-token' }
      });
    }
    return createResponse(404, { error: 'Not found' });
  } catch (error) {
    return createResponse(500, { error: 'Internal server error' });
  }
};

// Mock posts handler
const postsHandler = async (event) => {
  try {
    let body = {};
    if (event.body) {
      try {
        body = JSON.parse(event.body);
      } catch (e) {
        return createResponse(500, { error: 'Internal server error' });
      }
    }

    if (event.httpMethod === 'GET' && event.path === '/posts') {
      const AWS = require('../utils/aws-mocks');
      try {
        const scanResult = await AWS.mockDynamoDBDocumentClient.scan().promise();

        return createResponse(200, {
          posts: scanResult.Items || [],
          count: scanResult.Items ? scanResult.Items.length : 0
        });
      } catch (error) {
        return createResponse(500, { error: 'Failed to get posts' });
      }
    } else if (event.httpMethod === 'POST' && event.path === '/posts') {
      if (!body.title || !body.content || !body.userId) {
        return createResponse(400, { error: 'Title, content, and userId are required' });
      }

      const AWS = require('../utils/aws-mocks');
      await AWS.mockDynamoDBDocumentClient.put().promise();

      return createResponse(201, {
        message: 'Post created successfully',
        post: {
          id: 'test-post-id',
          title: body.title,
          content: body.content,
          userId: body.userId,
          mediaUrl: body.mediaUrl,
          likes: 0,
          comments: 0,
          createdAt: new Date().toISOString()
        }
      });
    } else if (event.httpMethod === 'GET' && event.pathParameters?.id) {
      const AWS = require('../utils/aws-mocks');
      try {
        const getResult = await AWS.mockDynamoDBDocumentClient.get().promise();
        if (!getResult.Item) {
          return createResponse(404, { error: 'Post not found' });
        }
        return createResponse(200, { post: getResult.Item });
      } catch (error) {
        return createResponse(500, { error: 'Internal server error' });
      }
    } else if (event.httpMethod === 'PUT' && event.pathParameters?.id) {
      if (!body.title && !body.content) {
        return createResponse(400, { error: 'At least one field (title or content) is required' });
      }

      const AWS = require('../utils/aws-mocks');
      await AWS.mockDynamoDBDocumentClient.update().promise();

      return createResponse(200, {
        message: 'Post updated successfully',
        post: {
          id: event.pathParameters.id,
          title: body.title,
          content: body.content,
          updatedAt: new Date().toISOString()
        }
      });
    } else if (event.httpMethod === 'POST' && event.path.includes('/like')) {
      if (!body.userId) {
        return createResponse(400, { error: 'userId is required' });
      }

      const AWS = require('../utils/aws-mocks');
      try {
        const existingLike = await AWS.mockDynamoDBDocumentClient.get().promise();
        if (existingLike.Item) {
          return createResponse(400, { error: 'Post already liked' });
        }

        await AWS.mockDynamoDBDocumentClient.put().promise();
        await AWS.mockDynamoDBDocumentClient.update().promise();

        return createResponse(200, { message: 'Post liked successfully' });
      } catch (error) {
        return createResponse(500, { error: 'Internal server error' });
      }
    } else if (event.httpMethod === 'DELETE' && event.path.includes('/like')) {
      if (!body.userId) {
        return createResponse(400, { error: 'userId is required' });
      }

      const AWS = require('../utils/aws-mocks');
      await AWS.mockDynamoDBDocumentClient.delete().promise();
      await AWS.mockDynamoDBDocumentClient.update().promise();

      return createResponse(200, { message: 'Post unliked successfully' });
    } else if (event.httpMethod === 'DELETE' && event.pathParameters?.id) {
      const AWS = require('../utils/aws-mocks');
      await AWS.mockDynamoDBDocumentClient.delete().promise();

      return createResponse(200, { message: 'Post deleted successfully' });
    }
    return createResponse(404, { error: 'Not found' });
  } catch (error) {
    return createResponse(500, { error: 'Internal server error' });
  }
};

// Mock media handler
const mediaHandler = async (event) => {
  try {
    let body = {};
    if (event.body) {
      try {
        body = JSON.parse(event.body);
      } catch (e) {
        return createResponse(500, { error: 'Internal server error' });
      }
    }

    if (event.httpMethod === 'POST' && event.path === '/media/upload') {
      if (!body.fileName || !body.fileType || !body.userId) {
        return createResponse(400, { error: 'fileName, fileType, and userId are required' });
      }

      const AWS = require('../utils/aws-mocks');
      try {
        await AWS.mockDynamoDBDocumentClient.put().promise();

        // Try to get signed URL - this might throw an error in tests
        let uploadUrl;
        try {
          uploadUrl = AWS.mockS3.getSignedUrl();
        } catch (s3Error) {
          return createResponse(500, { error: 'Failed to generate upload URL' });
        }

        return createResponse(200, {
          message: 'Upload URL generated successfully',
          mediaId: 'test-media-id',
          uploadUrl: uploadUrl || 'https://test-presigned-url.com',
          media: {
            id: 'test-media-id',
            fileName: body.fileName,
            fileType: body.fileType,
            userId: body.userId,
            status: 'pending',
            bucketName: body.mediaType === 'avatar' ? process.env.AVATARS_BUCKET : process.env.MEDIA_BUCKET,
            createdAt: new Date().toISOString()
          }
        });
      } catch (error) {
        if (error.message === 'S3 error') {
          return createResponse(500, { error: 'Failed to generate upload URL' });
        }
        return createResponse(500, { error: 'Internal server error' });
      }
    } else if (event.httpMethod === 'GET' && event.pathParameters?.id) {
      const AWS = require('../utils/aws-mocks');
      try {
        const getResult = await AWS.mockDynamoDBDocumentClient.get().promise();
        if (!getResult.Item) {
          return createResponse(404, { error: 'Media not found' });
        }

        const media = { ...getResult.Item };
        if (media.status === 'uploaded' || media.status === 'completed') {
          media.downloadUrl = 'https://test-download-url.com';
        }

        return createResponse(200, { media });
      } catch (error) {
        return createResponse(500, { error: 'Internal server error' });
      }
    } else if (event.httpMethod === 'DELETE' && event.pathParameters?.id) {
      const AWS = require('../utils/aws-mocks');
      try {
        const getResult = await AWS.mockDynamoDBDocumentClient.get().promise();
        if (!getResult.Item) {
          return createResponse(404, { error: 'Media not found' });
        }

        await AWS.mockDynamoDBDocumentClient.delete().promise();
        await AWS.mockS3.deleteObject().promise();

        return createResponse(200, { message: 'Media deleted successfully' });
      } catch (error) {
        if (error.message && error.message.includes('S3 deletion')) {
          return createResponse(500, { error: 'Failed to delete media' });
        }
        return createResponse(500, { error: 'Internal server error' });
      }
    } else if (event.httpMethod === 'PUT' && event.pathParameters?.id) {
      if (!body.status) {
        return createResponse(400, { error: 'Status is required' });
      }

      const AWS = require('../utils/aws-mocks');
      await AWS.mockDynamoDBDocumentClient.update().promise();

      return createResponse(200, {
        message: 'Media status updated successfully',
        media: {
          id: event.pathParameters.id,
          status: body.status,
          updatedAt: new Date().toISOString()
        }
      });
    }
    return createResponse(404, { error: 'Not found' });
  } catch (error) {
    return createResponse(500, { error: 'Internal server error' });
  }
};

// Mock users handler
const usersHandler = async (event) => {
  try {
    let body = {};
    if (event.body) {
      try {
        body = JSON.parse(event.body);
      } catch (e) {
        return createResponse(500, { error: 'Internal server error' });
      }
    }

    if (event.httpMethod === 'GET' && event.path === '/users/profile') {
      const { userId } = event.queryStringParameters || {};

      if (!userId) {
        return createResponse(400, { error: 'userId is required' });
      }

      // Check for mocked DynamoDB responses
      const AWS = require('../utils/aws-mocks');
      try {
        const userResult = await AWS.mockDynamoDBDocumentClient.get().promise();
        if (!userResult.Item) {
          return createResponse(404, { error: 'User not found' });
        }

        const profileResult = await AWS.mockDynamoDBDocumentClient.get().promise();
        const profile = profileResult.Item || {};

        return createResponse(200, {
          user: {
            id: userResult.Item.id,
            email: userResult.Item.email,
            username: userResult.Item.username,
            bio: profile.bio || '',
            followersCount: profile.followersCount || 0,
            followingCount: profile.followingCount || 0,
            postsCount: profile.postsCount || 0
          }
        });
      } catch (error) {
        return createResponse(500, { error: 'Internal server error' });
      }
    } else if (event.httpMethod === 'PUT' && event.path === '/users/profile') {
      if (!body.userId) {
        return createResponse(400, { error: 'userId is required' });
      }

      const AWS = require('../utils/aws-mocks');
      await AWS.mockDynamoDBDocumentClient.update().promise();

      return createResponse(200, { message: 'Profile updated successfully' });
    } else if (event.httpMethod === 'GET' && event.pathParameters?.id) {
      const AWS = require('../utils/aws-mocks');
      try {
        const userResult = await AWS.mockDynamoDBDocumentClient.get().promise();
        if (!userResult.Item) {
          return createResponse(404, { error: 'User not found' });
        }

        const profileResult = await AWS.mockDynamoDBDocumentClient.get().promise();
        const profile = profileResult.Item || {};

        return createResponse(200, {
          user: {
            id: userResult.Item.id,
            username: userResult.Item.username,
            // Don't include email for privacy
            bio: profile.bio || '',
            followersCount: profile.followersCount || 0,
            followingCount: profile.followingCount || 0,
            postsCount: profile.postsCount || 0
          }
        });
      } catch (error) {
        return createResponse(500, { error: 'Internal server error' });
      }
    } else if (event.httpMethod === 'POST' && event.path.includes('/follow')) {
      if (!body.userId) {
        return createResponse(400, { error: 'userId is required' });
      }

      const targetUserId = event.pathParameters?.id;
      if (body.userId === targetUserId) {
        return createResponse(400, { error: 'Cannot follow yourself' });
      }

      // Check if already following
      const AWS = require('../utils/aws-mocks');
      try {
        const existingFollow = await AWS.mockDynamoDBDocumentClient.get().promise();
        if (existingFollow.Item) {
          return createResponse(400, { error: 'Already following this user' });
        }

        await AWS.mockDynamoDBDocumentClient.put().promise();
        await AWS.mockDynamoDBDocumentClient.update().promise();

        return createResponse(200, { message: 'User followed successfully' });
      } catch (error) {
        return createResponse(500, { error: 'Internal server error' });
      }
    } else if (event.httpMethod === 'DELETE' && event.path.includes('/follow')) {
      if (!body.userId) {
        return createResponse(400, { error: 'userId is required' });
      }

      const AWS = require('../utils/aws-mocks');
      await AWS.mockDynamoDBDocumentClient.delete().promise();
      await AWS.mockDynamoDBDocumentClient.update().promise();

      return createResponse(200, { message: 'User unfollowed successfully' });
    }
    return createResponse(404, { error: 'Not found' });
  } catch (error) {
    return createResponse(500, { error: 'Internal server error' });
  }
};

module.exports = {
  healthHandler,
  authHandler,
  postsHandler,
  mediaHandler,
  usersHandler
};
