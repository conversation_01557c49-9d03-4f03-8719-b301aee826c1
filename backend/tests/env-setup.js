/**
 * Environment setup for tests
 * Sets up environment variables before tests run
 */

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.ENVIRONMENT = 'test';
process.env.PROJECT_NAME = 'gameflex';
process.env.AWS_REGION = 'us-east-1';

// Mock AWS services for testing
process.env.USER_POOL_ID = 'us-east-1_TestPool123';
process.env.USER_POOL_CLIENT_ID = 'test-client-id-123';
process.env.USERS_TABLE = 'gameflex-test-Users';
process.env.POSTS_TABLE = 'gameflex-test-Posts';
process.env.MEDIA_TABLE = 'gameflex-test-Media';
process.env.USER_PROFILES_TABLE = 'gameflex-test-UserProfiles';
process.env.COMMENTS_TABLE = 'gameflex-test-Comments';
process.env.LIKES_TABLE = 'gameflex-test-Likes';
process.env.FOLLOWS_TABLE = 'gameflex-test-Follows';
process.env.MEDIA_BUCKET = 'gameflex-media-test';
process.env.AVATARS_BUCKET = 'gameflex-avatars-test';
process.env.TEMP_BUCKET = 'gameflex-temp-test';

// Disable AWS SDK retries for faster tests
process.env.AWS_MAX_ATTEMPTS = '1';

console.log('Test environment variables set up');
