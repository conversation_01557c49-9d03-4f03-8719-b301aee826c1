/**
 * Jest setup file
 * Runs after the test framework has been installed in the environment
 */

// Increase timeout for integration tests
jest.setTimeout(30000);

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  // Uncomment to suppress console.log in tests
  // log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Global test utilities
global.testUtils = {
  delay: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Helper to create mock AWS responses
  createMockAWSResponse: (data) => ({
    promise: () => Promise.resolve(data)
  }),
  
  // Helper to create mock AWS error
  createMockAWSError: (code, message) => {
    const error = new Error(message);
    error.code = code;
    return {
      promise: () => Promise.reject(error)
    };
  }
};

console.log('Jest setup completed');
