/**
 * API Response Format Tests
 * Tests the basic response structure and CORS headers
 */

describe('API Response Format', () => {
  // Helper function to create API Gateway response
  const createResponse = (statusCode, body) => ({
    statusCode,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
      'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
  });

  describe('Response Structure', () => {
    it('should create proper success response', () => {
      const response = createResponse(200, { message: 'Success', data: { id: 1 } });
      
      expect(response.statusCode).toBe(200);
      expect(response.headers['Content-Type']).toBe('application/json');
      
      const body = JSON.parse(response.body);
      expect(body.message).toBe('Success');
      expect(body.data.id).toBe(1);
    });

    it('should create proper error response', () => {
      const response = createResponse(400, { error: 'Bad Request', details: 'Invalid input' });
      
      expect(response.statusCode).toBe(400);
      
      const body = JSON.parse(response.body);
      expect(body.error).toBe('Bad Request');
      expect(body.details).toBe('Invalid input');
    });

    it('should include CORS headers', () => {
      const response = createResponse(200, { status: 'ok' });
      
      expect(response.headers['Access-Control-Allow-Origin']).toBe('*');
      expect(response.headers['Access-Control-Allow-Headers']).toContain('Content-Type');
      expect(response.headers['Access-Control-Allow-Methods']).toContain('GET');
      expect(response.headers['Access-Control-Allow-Methods']).toContain('POST');
    });
  });

  describe('HTTP Status Codes', () => {
    it('should handle 200 OK responses', () => {
      const response = createResponse(200, { status: 'healthy' });
      expect(response.statusCode).toBe(200);
    });

    it('should handle 201 Created responses', () => {
      const response = createResponse(201, { message: 'Resource created' });
      expect(response.statusCode).toBe(201);
    });

    it('should handle 400 Bad Request responses', () => {
      const response = createResponse(400, { error: 'Bad Request' });
      expect(response.statusCode).toBe(400);
    });

    it('should handle 401 Unauthorized responses', () => {
      const response = createResponse(401, { error: 'Unauthorized' });
      expect(response.statusCode).toBe(401);
    });

    it('should handle 404 Not Found responses', () => {
      const response = createResponse(404, { error: 'Not found' });
      expect(response.statusCode).toBe(404);
    });

    it('should handle 500 Internal Server Error responses', () => {
      const response = createResponse(500, { error: 'Internal server error' });
      expect(response.statusCode).toBe(500);
    });
  });

  describe('JSON Serialization', () => {
    it('should serialize simple objects', () => {
      const data = { name: 'test', value: 123 };
      const response = createResponse(200, data);
      
      const parsed = JSON.parse(response.body);
      expect(parsed.name).toBe('test');
      expect(parsed.value).toBe(123);
    });

    it('should serialize arrays', () => {
      const data = { items: [1, 2, 3], count: 3 };
      const response = createResponse(200, data);
      
      const parsed = JSON.parse(response.body);
      expect(parsed.items).toEqual([1, 2, 3]);
      expect(parsed.count).toBe(3);
    });

    it('should serialize nested objects', () => {
      const data = {
        user: {
          id: 1,
          profile: {
            name: 'Test User',
            settings: { theme: 'dark' }
          }
        }
      };
      const response = createResponse(200, data);
      
      const parsed = JSON.parse(response.body);
      expect(parsed.user.id).toBe(1);
      expect(parsed.user.profile.name).toBe('Test User');
      expect(parsed.user.profile.settings.theme).toBe('dark');
    });
  });
});
