/**
 * Unit tests for Posts Lambda function
 */

const { TestDataGenerator, TEST_USERS } = require('../utils/test-data');
const {
  resetAllMocks,
  mockDynamoDBGet,
  mockDynamoDBPut,
  mockDynamoDBUpdate,
  mockDynamoDBScan,
  mockDynamoDBDocumentClient
} = require('../utils/aws-mocks');
const { postsHandler } = require('../utils/lambda-mocks');

// Use the mock handler for testing
const handler = postsHandler;

describe('Posts Lambda Handler', () => {
  beforeEach(() => {
    resetAllMocks();
  });

  describe('GET /posts', () => {
    it('should return all posts successfully', async () => {
      const testPosts = [
        TestDataGenerator.createPost(TEST_USERS.VALID_USER.id),
        TestDataGenerator.createPost(TEST_USERS.ADMIN_USER.id)
      ];

      mockDynamoDBScan(testPosts);

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/posts'
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.posts).toBeDefined();
      expect(body.posts).toHaveLength(2);
      expect(body.count).toBe(2);
    });

    it('should return empty array when no posts exist', async () => {
      mockDynamoDBScan([]);

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/posts'
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.posts).toEqual([]);
      expect(body.count).toBe(0);
    });

    it('should handle DynamoDB errors', async () => {
      mockDynamoDBDocumentClient.scan.mockImplementation(() => ({
        promise: () => Promise.reject(new Error('DynamoDB error'))
      }));

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/posts'
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Failed to get posts');
    });
  });

  describe('GET /posts/{id}', () => {
    it('should return a specific post successfully', async () => {
      const testPost = TestDataGenerator.createPost(TEST_USERS.VALID_USER.id);
      mockDynamoDBGet(testPost);

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: `/posts/${testPost.id}`,
        pathParameters: { id: testPost.id }
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.post).toBeDefined();
      expect(body.post.id).toBe(testPost.id);
      expect(body.post.title).toBe(testPost.title);
    });

    it('should return 404 when post is not found', async () => {
      mockDynamoDBGet(null);

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/posts/nonexistent-id',
        pathParameters: { id: 'nonexistent-id' }
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(404);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Post not found');
    });
  });

  describe('POST /posts', () => {
    it('should create a new post successfully', async () => {
      mockDynamoDBPut();

      const postData = {
        title: 'Test Post',
        content: 'This is a test post content',
        userId: TEST_USERS.VALID_USER.id
      };

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/posts',
        body: JSON.stringify(postData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(201);
      const body = JSON.parse(result.body);
      expect(body.message).toBe('Post created successfully');
      expect(body.post).toBeDefined();
      expect(body.post.title).toBe(postData.title);
      expect(body.post.content).toBe(postData.content);
      expect(body.post.userId).toBe(postData.userId);
      expect(body.post.id).toBeDefined();
      expect(body.post.likes).toBe(0);
      expect(body.post.comments).toBe(0);
    });

    it('should return 400 when required fields are missing', async () => {
      const testCases = [
        { title: 'Test Post', content: 'Content' }, // Missing userId
        { title: 'Test Post', userId: TEST_USERS.VALID_USER.id }, // Missing content
        { content: 'Content', userId: TEST_USERS.VALID_USER.id }, // Missing title
        {} // All missing
      ];

      for (const testCase of testCases) {
        const event = TestDataGenerator.createAPIGatewayEvent({
          httpMethod: 'POST',
          path: '/posts',
          body: JSON.stringify(testCase)
        });
        const context = TestDataGenerator.createLambdaContext();

        const result = await handler(event, context);

        expect(result.statusCode).toBe(400);
        const body = JSON.parse(result.body);
        expect(body.error).toBe('Title, content, and userId are required');
      }
    });

    it('should create post with optional mediaUrl', async () => {
      mockDynamoDBPut();

      const postData = {
        title: 'Test Post with Media',
        content: 'This is a test post with media',
        userId: TEST_USERS.VALID_USER.id,
        mediaUrl: 'https://example.com/image.jpg'
      };

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/posts',
        body: JSON.stringify(postData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(201);
      const body = JSON.parse(result.body);
      expect(body.post.mediaUrl).toBe(postData.mediaUrl);
    });
  });

  describe('PUT /posts/{id}', () => {
    it('should update a post successfully', async () => {
      const updatedPost = TestDataGenerator.createPost(TEST_USERS.VALID_USER.id, {
        title: 'Updated Title',
        content: 'Updated content'
      });

      mockDynamoDBUpdate(updatedPost);

      const updateData = {
        title: 'Updated Title',
        content: 'Updated content'
      };

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'PUT',
        path: `/posts/${updatedPost.id}`,
        pathParameters: { id: updatedPost.id },
        body: JSON.stringify(updateData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.message).toBe('Post updated successfully');
      expect(body.post).toBeDefined();
      expect(body.post.title).toBe(updateData.title);
      expect(body.post.content).toBe(updateData.content);
    });

    it('should update only provided fields', async () => {
      const originalPost = TestDataGenerator.createPost(TEST_USERS.VALID_USER.id);
      const updatedPost = { ...originalPost, title: 'Updated Title Only' };

      mockDynamoDBUpdate(updatedPost);

      const updateData = {
        title: 'Updated Title Only'
      };

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'PUT',
        path: `/posts/${originalPost.id}`,
        pathParameters: { id: originalPost.id },
        body: JSON.stringify(updateData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.post.title).toBe(updateData.title);
    });
  });

  describe('DELETE /posts/{id}', () => {
    it('should delete a post successfully', async () => {
      mockDynamoDBDocumentClient.delete.mockImplementation(() => ({
        promise: () => Promise.resolve({})
      }));

      const postId = 'test-post-id';

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'DELETE',
        path: `/posts/${postId}`,
        pathParameters: { id: postId }
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.message).toBe('Post deleted successfully');
    });
  });

  describe('POST /posts/{id}/like', () => {
    it('should like a post successfully', async () => {
      // Mock that like doesn't exist
      mockDynamoDBGet(null);

      // Mock successful put and update operations
      mockDynamoDBPut();
      mockDynamoDBDocumentClient.update.mockImplementation(() => ({
        promise: () => Promise.resolve({})
      }));

      const postId = 'test-post-id';
      const likeData = {
        userId: TEST_USERS.VALID_USER.id
      };

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: `/posts/${postId}/like`,
        pathParameters: { id: postId },
        body: JSON.stringify(likeData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.message).toBe('Post liked successfully');
    });

    it('should return 400 when userId is missing', async () => {
      const postId = 'test-post-id';

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: `/posts/${postId}/like`,
        pathParameters: { id: postId },
        body: JSON.stringify({})
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('userId is required');
    });

    it('should return 400 when post is already liked', async () => {
      // Mock that like already exists
      mockDynamoDBGet({ post_id: 'test-post-id', user_id: TEST_USERS.VALID_USER.id });

      const postId = 'test-post-id';
      const likeData = {
        userId: TEST_USERS.VALID_USER.id
      };

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: `/posts/${postId}/like`,
        pathParameters: { id: postId },
        body: JSON.stringify(likeData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Post already liked');
    });
  });

  describe('DELETE /posts/{id}/like', () => {
    it('should unlike a post successfully', async () => {
      mockDynamoDBDocumentClient.delete.mockImplementation(() => ({
        promise: () => Promise.resolve({})
      }));

      mockDynamoDBDocumentClient.update.mockImplementation(() => ({
        promise: () => Promise.resolve({})
      }));

      const postId = 'test-post-id';
      const unlikeData = {
        userId: TEST_USERS.VALID_USER.id
      };

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'DELETE',
        path: `/posts/${postId}/like`,
        pathParameters: { id: postId },
        body: JSON.stringify(unlikeData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.message).toBe('Post unliked successfully');
    });

    it('should return 400 when userId is missing', async () => {
      const postId = 'test-post-id';

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'DELETE',
        path: `/posts/${postId}/like`,
        pathParameters: { id: postId },
        body: JSON.stringify({})
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('userId is required');
    });
  });

  describe('Error Handling', () => {
    it('should return 404 for unknown endpoints', async () => {
      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/posts/unknown'
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(404);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Not found');
    });

    it('should include CORS headers in all responses', async () => {
      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/posts/unknown'
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.headers).toBeDefined();
      expect(result.headers['Access-Control-Allow-Origin']).toBe('*');
      expect(result.headers['Access-Control-Allow-Headers']).toBeDefined();
      expect(result.headers['Access-Control-Allow-Methods']).toBeDefined();
    });
  });
});
