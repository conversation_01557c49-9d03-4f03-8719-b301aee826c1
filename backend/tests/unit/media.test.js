/**
 * Unit tests for Media Lambda function
 */

const { TestDataGenerator, TEST_USERS } = require('../utils/test-data');
const {
  resetAllMocks,
  mockDynamoDBGet,
  mockDynamoDBPut,
  mockDynamoDBUpdate,
  mockS3GetSignedUrl,
  mockDynamoDBDocumentClient,
  mockS3
} = require('../utils/aws-mocks');
const { mediaHandler } = require('../utils/lambda-mocks');

// Use the mock handler for testing
const handler = mediaHandler;

describe('Media Lambda Handler', () => {
  beforeEach(() => {
    resetAllMocks();
  });

  describe('POST /media/upload', () => {
    it('should generate upload URL successfully', async () => {
      mockDynamoDBPut();
      mockS3GetSignedUrl('https://test-presigned-url.com');

      const uploadData = {
        fileName: 'test-image.jpg',
        fileType: 'image/jpeg',
        fileSize: 1024000,
        mediaType: 'image',
        userId: TEST_USERS.VALID_USER.id
      };

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/media/upload',
        body: JSON.stringify(uploadData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.message).toBe('Upload URL generated successfully');
      expect(body.mediaId).toBeDefined();
      expect(body.uploadUrl).toBe('https://test-presigned-url.com');
      expect(body.media).toBeDefined();
      expect(body.media.fileName).toBe(uploadData.fileName);
      expect(body.media.fileType).toBe(uploadData.fileType);
      expect(body.media.userId).toBe(uploadData.userId);
      expect(body.media.status).toBe('pending');
    });

    it('should return 400 when required fields are missing', async () => {
      const testCases = [
        { fileName: 'test.jpg', fileType: 'image/jpeg' }, // Missing userId
        { fileName: 'test.jpg', userId: TEST_USERS.VALID_USER.id }, // Missing fileType
        { fileType: 'image/jpeg', userId: TEST_USERS.VALID_USER.id }, // Missing fileName
        {} // All missing
      ];

      for (const testCase of testCases) {
        const event = TestDataGenerator.createAPIGatewayEvent({
          httpMethod: 'POST',
          path: '/media/upload',
          body: JSON.stringify(testCase)
        });
        const context = TestDataGenerator.createLambdaContext();

        const result = await handler(event, context);

        expect(result.statusCode).toBe(400);
        const body = JSON.parse(result.body);
        expect(body.error).toBe('fileName, fileType, and userId are required');
      }
    });

    it('should use avatars bucket for avatar media type', async () => {
      mockDynamoDBPut();
      mockS3GetSignedUrl('https://test-presigned-url.com');

      const uploadData = {
        fileName: 'avatar.jpg',
        fileType: 'image/jpeg',
        mediaType: 'avatar',
        userId: TEST_USERS.VALID_USER.id
      };

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/media/upload',
        body: JSON.stringify(uploadData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.media.bucketName).toBe(process.env.AVATARS_BUCKET);
    });

    it('should use media bucket for non-avatar media types', async () => {
      mockDynamoDBPut();
      mockS3GetSignedUrl('https://test-presigned-url.com');

      const uploadData = {
        fileName: 'post-image.jpg',
        fileType: 'image/jpeg',
        mediaType: 'image',
        userId: TEST_USERS.VALID_USER.id
      };

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/media/upload',
        body: JSON.stringify(uploadData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.media.bucketName).toBe(process.env.MEDIA_BUCKET);
    });

    it('should handle S3 errors', async () => {
      mockDynamoDBPut(); // Set up DynamoDB to succeed
      mockS3.getSignedUrl.mockImplementation(() => {
        throw new Error('S3 error');
      });

      const uploadData = {
        fileName: 'test-image.jpg',
        fileType: 'image/jpeg',
        userId: TEST_USERS.VALID_USER.id
      };

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/media/upload',
        body: JSON.stringify(uploadData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Failed to generate upload URL');
    });
  });

  describe('GET /media/{id}', () => {
    it('should return media info successfully', async () => {
      const testMedia = TestDataGenerator.createMedia(TEST_USERS.VALID_USER.id, {
        status: 'uploaded'
      });

      mockDynamoDBGet(testMedia);
      mockS3GetSignedUrl('https://test-download-url.com');

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: `/media/${testMedia.id}`,
        pathParameters: { id: testMedia.id }
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.media).toBeDefined();
      expect(body.media.id).toBe(testMedia.id);
      expect(body.media.downloadUrl).toBe('https://test-download-url.com');
    });

    it('should return 404 when media is not found', async () => {
      mockDynamoDBGet(null);

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/media/nonexistent-id',
        pathParameters: { id: 'nonexistent-id' }
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(404);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Media not found');
    });

    it('should not include download URL for pending media', async () => {
      const testMedia = TestDataGenerator.createMedia(TEST_USERS.VALID_USER.id, {
        status: 'pending'
      });

      mockDynamoDBGet(testMedia);

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: `/media/${testMedia.id}`,
        pathParameters: { id: testMedia.id }
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.media.downloadUrl).toBeUndefined();
    });
  });

  describe('DELETE /media/{id}', () => {
    it('should delete media successfully', async () => {
      const testMedia = TestDataGenerator.createMedia(TEST_USERS.VALID_USER.id);

      mockDynamoDBGet(testMedia);

      mockS3.deleteObject.mockImplementation(() => ({
        promise: () => Promise.resolve({})
      }));

      mockDynamoDBDocumentClient.delete.mockImplementation(() => ({
        promise: () => Promise.resolve({})
      }));

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'DELETE',
        path: `/media/${testMedia.id}`,
        pathParameters: { id: testMedia.id }
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.message).toBe('Media deleted successfully');
    });

    it('should return 404 when media is not found', async () => {
      mockDynamoDBGet(null);

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'DELETE',
        path: '/media/nonexistent-id',
        pathParameters: { id: 'nonexistent-id' }
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(404);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Media not found');
    });

    it('should handle S3 deletion errors gracefully', async () => {
      const testMedia = TestDataGenerator.createMedia(TEST_USERS.VALID_USER.id);

      mockDynamoDBGet(testMedia);

      // Set up DynamoDB delete to succeed
      mockDynamoDBDocumentClient.delete.mockImplementation(() => ({
        promise: () => Promise.resolve()
      }));

      mockS3.deleteObject.mockImplementation(() => ({
        promise: () => Promise.reject(new Error('S3 deletion failed'))
      }));

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'DELETE',
        path: `/media/${testMedia.id}`,
        pathParameters: { id: testMedia.id }
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Failed to delete media');
    });
  });

  describe('PUT /media/{id}', () => {
    it('should update media status successfully', async () => {
      const updatedMedia = TestDataGenerator.createMedia(TEST_USERS.VALID_USER.id, {
        status: 'uploaded'
      });

      mockDynamoDBUpdate(updatedMedia);

      const updateData = {
        status: 'uploaded'
      };

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'PUT',
        path: `/media/${updatedMedia.id}`,
        pathParameters: { id: updatedMedia.id },
        body: JSON.stringify(updateData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.message).toBe('Media status updated successfully');
      expect(body.media).toBeDefined();
      expect(body.media.status).toBe('uploaded');
    });

    it('should return 400 when status is missing', async () => {
      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'PUT',
        path: '/media/test-id',
        pathParameters: { id: 'test-id' },
        body: JSON.stringify({})
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Status is required');
    });
  });

  describe('Error Handling', () => {
    it('should return 404 for unknown endpoints', async () => {
      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/media/unknown'
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(404);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Not found');
    });

    it('should include CORS headers in all responses', async () => {
      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/media/unknown'
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.headers).toBeDefined();
      expect(result.headers['Access-Control-Allow-Origin']).toBe('*');
      expect(result.headers['Access-Control-Allow-Headers']).toBeDefined();
      expect(result.headers['Access-Control-Allow-Methods']).toBeDefined();
    });
  });
});
