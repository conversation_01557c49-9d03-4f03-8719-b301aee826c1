/**
 * Unit tests for Health Check Lambda function
 */

const { TestDataGenerator } = require('../utils/test-data');
const { resetAllMocks, mockDynamoDBScan } = require('../utils/aws-mocks');
const { healthHandler } = require('../utils/lambda-mocks');

// Use the mock handler for testing
const handler = healthHandler;

describe('Health Check Lambda Handler', () => {
  beforeEach(() => {
    resetAllMocks();
  });

  describe('GET /health', () => {
    it('should return health status with all services healthy', async () => {
      // Mock successful DynamoDB scan
      const { mockDynamoDBDocumentClient } = require('../utils/aws-mocks');
      mockDynamoDBDocumentClient.scan.mockImplementation(() => ({
        promise: () => Promise.resolve({ Items: [] })
      }));

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/health'
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);

      const body = JSON.parse(result.body);
      expect(body.status).toBe('healthy');
      expect(body.timestamp).toBeDefined();
      expect(body.environment).toBe('test');
      expect(body.version).toBe('1.0.0');
      expect(body.services).toBeDefined();
      expect(body.services.database).toBe('healthy');
      expect(body.services.api).toBe('healthy');
      expect(body.uptime).toBeDefined();
      expect(body.memory).toBeDefined();
      expect(body.environment_variables).toBeDefined();
    });

    it('should return health status with database unhealthy when DynamoDB fails', async () => {
      // Mock DynamoDB error
      const mockError = new Error('DynamoDB connection failed');
      require('../utils/aws-mocks').mockDynamoDBDocumentClient.scan.mockImplementation(() => ({
        promise: () => Promise.reject(mockError)
      }));

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/health'
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);

      const body = JSON.parse(result.body);
      expect(body.status).toBe('healthy');
      expect(body.services.database).toBe('unhealthy');
      expect(body.services.api).toBe('healthy');
    });

    it('should include correct CORS headers', async () => {
      mockDynamoDBScan([]);

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/health'
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.headers).toBeDefined();
      expect(result.headers['Access-Control-Allow-Origin']).toBe('*');
      expect(result.headers['Access-Control-Allow-Headers']).toContain('Content-Type');
      expect(result.headers['Access-Control-Allow-Methods']).toContain('GET');
      expect(result.headers['Content-Type']).toBe('application/json');
    });

    it('should include environment variables status', async () => {
      mockDynamoDBScan([]);

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/health'
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      const body = JSON.parse(result.body);
      expect(body.environment_variables).toBeDefined();
      expect(body.environment_variables.USER_POOL_ID).toBe('set');
      expect(body.environment_variables.USER_POOL_CLIENT_ID).toBe('set');
      expect(body.environment_variables.USERS_TABLE).toBe('set');
      expect(body.environment_variables.POSTS_TABLE).toBe('set');
      expect(body.environment_variables.MEDIA_BUCKET).toBe('set');
    });

    it('should include memory usage information', async () => {
      mockDynamoDBScan([]);

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/health'
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      const body = JSON.parse(result.body);
      expect(body.memory).toBeDefined();
      expect(typeof body.memory.rss).toBe('number');
      expect(typeof body.memory.heapTotal).toBe('number');
      expect(typeof body.memory.heapUsed).toBe('number');
      expect(typeof body.memory.external).toBe('number');
    });

    it('should include uptime information', async () => {
      mockDynamoDBScan([]);

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/health'
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      const body = JSON.parse(result.body);
      expect(body.uptime).toBeDefined();
      expect(typeof body.uptime).toBe('number');
      expect(body.uptime).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Error Handling', () => {
    it('should return 404 for non-health endpoints', async () => {
      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/unknown'
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(404);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Not found');
    });

    it('should return 500 for unexpected errors', async () => {
      // Mock an unexpected error by making process.uptime throw
      const originalUptime = process.uptime;
      process.uptime = jest.fn(() => {
        throw new Error('Unexpected error');
      });

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/health'
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.status).toBe('unhealthy');
      expect(body.error).toBe('Unexpected error');

      // Restore original function
      process.uptime = originalUptime;
    });

    it('should handle missing environment variables gracefully', async () => {
      // Temporarily remove environment variables
      const originalUserPoolId = process.env.USER_POOL_ID;
      delete process.env.USER_POOL_ID;

      mockDynamoDBScan([]);

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/health'
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.environment_variables.USER_POOL_ID).toBe('not set');

      // Restore environment variable
      process.env.USER_POOL_ID = originalUserPoolId;
    });
  });

  describe('HTTP Methods', () => {
    it('should only accept GET requests', async () => {
      const methods = ['POST', 'PUT', 'DELETE', 'PATCH'];

      for (const method of methods) {
        const event = TestDataGenerator.createAPIGatewayEvent({
          httpMethod: method,
          path: '/health'
        });
        const context = TestDataGenerator.createLambdaContext();

        const result = await handler(event, context);

        expect(result.statusCode).toBe(404);
        const body = JSON.parse(result.body);
        expect(body.error).toBe('Not found');
      }
    });
  });
});
