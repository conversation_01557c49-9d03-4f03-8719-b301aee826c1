#!/usr/bin/env pwsh
# Staging Run Script for GameFlex Mobile
# This script runs the app in staging mode with dev.api.gameflex.io URLs

param(
    [string]$Platform = "windows",
    [string]$Device = "",
    [switch]$Help = $false
)

if ($Help) {
    Write-Host "GameFlex Mobile Staging Run Script" -ForegroundColor Green
    Write-Host ""
    Write-Host "Usage: .\run-staging.ps1 [OPTIONS]" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Yellow
    Write-Host "  -Platform <platform>  Target platform (windows, android, ios, web) [default: windows]"
    Write-Host "  -Device <device>      Specific device ID (optional, will prompt if not provided)"
    Write-Host "  -Help                 Show this help message"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\run-staging.ps1                              # Run Windows staging"
    Write-Host "  .\run-staging.ps1 -Platform android            # Run Android staging (will prompt for device)"
    Write-Host "  .\run-staging.ps1 -Platform android -Device 3C221FDJG0023G  # Run on specific Android device"
    Write-Host "  .\run-staging.ps1 -Platform ios                # Run iOS staging"
    Write-Host "  .\run-staging.ps1 -Platform web                # Run Web staging"
    Write-Host ""
    Write-Host "⚠️  Warning: This connects to staging servers!" -ForegroundColor Red
    exit 0
}

# Function to get available devices and let user select
function Get-AndroidDevice {
    param([string]$PreferredDevice)

    if ($PreferredDevice) {
        Write-Host "Using specified device: $PreferredDevice" -ForegroundColor Yellow
        return $PreferredDevice
    }

    Write-Host "📱 Getting available devices..." -ForegroundColor Yellow
    $devicesOutput = flutter devices 2>&1

    # Parse Android devices from output
    $androidDevices = @()
    $devicesOutput | ForEach-Object {
        $line = $_.ToString()

        if ($line -match "^\s+(.+?)\s+\(mobile\)\s+.+?\s+(.+?)\s+.+?\s+android-") {
            $androidDevices += @{
                Name = $matches[1].Trim()
                Id   = $matches[2].Trim()
            }
        }
    }

    if ($androidDevices.Count -eq 0) {
        Write-Host "❌ No Android devices found" -ForegroundColor Red
        exit 1
    }

    if ($androidDevices.Count -eq 1) {
        $device = $androidDevices[0]
        Write-Host "Found 1 Android device: $($device.Name) ($($device.Id))" -ForegroundColor Green
        return $device.Id
    }

    # Multiple devices - let user choose
    Write-Host "Found $($androidDevices.Count) Android devices:" -ForegroundColor Yellow
    for ($i = 0; $i -lt $androidDevices.Count; $i++) {
        $device = $androidDevices[$i]
        Write-Host "  [$($i + 1)] $($device.Name) ($($device.Id))" -ForegroundColor White
    }

    do {
        $choice = Read-Host "Select device (1-$($androidDevices.Count))"
        $choiceNum = [int]$choice - 1
    } while ($choiceNum -lt 0 -or $choiceNum -ge $androidDevices.Count)

    $selectedDevice = $androidDevices[$choiceNum]
    Write-Host "Selected: $($selectedDevice.Name) ($($selectedDevice.Id))" -ForegroundColor Green
    return $selectedDevice.Id
}

Write-Host "🚀 Running GameFlex Mobile in STAGING mode" -ForegroundColor Green
Write-Host "Platform: $Platform" -ForegroundColor Yellow
Write-Host "Mode: Release (Staging)" -ForegroundColor Yellow
Write-Host ""

# Ensure we're in the correct directory
if (!(Test-Path "pubspec.yaml")) {
    Write-Host "❌ Error: pubspec.yaml not found. Please run this script from the Flutter project root." -ForegroundColor Red
    exit 1
}

# Get dependencies
Write-Host "📦 Getting dependencies..." -ForegroundColor Yellow
flutter pub get
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Flutter pub get failed" -ForegroundColor Red
    exit 1
}

Write-Host "🔧 Staging Configuration:" -ForegroundColor Yellow
Write-Host "  - Uses staging backend (https://dev.api.gameflex.io)" -ForegroundColor White
Write-Host "  - Release mode optimizations" -ForegroundColor White
Write-Host "  - Staging app name/icon" -ForegroundColor White
Write-Host ""
Write-Host "⚠️  Warning: This will connect to staging servers!" -ForegroundColor Red
Write-Host ""



switch ($Platform.ToLower()) {
    "windows" {
        Write-Host "🏃 Running on Windows (staging)..." -ForegroundColor Yellow
        flutter run -d windows --release --dart-define=STAGING=true
    }
    "android" {
        $androidDevice = Get-AndroidDevice -PreferredDevice $Device
        Write-Host "🏃 Running on Android (staging)..." -ForegroundColor Yellow
        flutter run -d $androidDevice --release --flavor staging --dart-define=STAGING=true
    }
    "ios" {
        Write-Host "🏃 Running on iOS (staging)..." -ForegroundColor Yellow
        flutter run -d ios --release --dart-define=STAGING=true
    }
    "web" {
        Write-Host "🏃 Running on Web (staging)..." -ForegroundColor Yellow
        flutter run -d web-server --release --dart-define=STAGING=true --web-port 3001
    }
    default {
        Write-Host "❌ Unsupported platform: $Platform" -ForegroundColor Red
        Write-Host "Supported platforms: windows, android, ios, web" -ForegroundColor Yellow
        exit 1
    }
}
