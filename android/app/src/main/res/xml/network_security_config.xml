<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- Base configuration for all domains -->
    <base-config cleartextTrafficPermitted="true">
        <trust-anchors>
            <!-- Trust system CAs -->
            <certificates src="system"/>
        </trust-anchors>
    </base-config>

    <!-- Allow cleartext traffic for development and staging -->
    <domain-config cleartextTrafficPermitted="true">
        <!-- Development domains -->
        <domain includeSubdomains="true">gameflex.local</domain>
        <domain includeSubdomains="true">api.gameflex.local</domain>

        <!-- Staging domains -->
        <domain includeSubdomains="true">dev.api.gameflex.io</domain>
        <domain includeSubdomains="true">gameflex.io</domain>

        <!-- Local development servers -->
        <domain includeSubdomains="true">********</domain>
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">127.0.0.1</domain>
        <domain includeSubdomains="true">***********</domain>
        <domain includeSubdomains="true">***********</domain>

        <!-- Local network ranges -->
        <domain includeSubdomains="true">***********/24</domain>
        <domain includeSubdomains="true">***********/24</domain>
    </domain-config>

    <!-- Allow HTTPS traffic for external services -->
    <domain-config cleartextTrafficPermitted="false">
        <!-- Google services -->
        <domain includeSubdomains="true">fonts.gstatic.com</domain>
        <domain includeSubdomains="true">fonts.googleapis.com</domain>
        <domain includeSubdomains="true">googleapis.com</domain>

        <!-- Supabase services -->
        <domain includeSubdomains="true">supabase.co</domain>
        <domain includeSubdomains="true">supabase.com</domain>
    </domain-config>
</network-security-config>
